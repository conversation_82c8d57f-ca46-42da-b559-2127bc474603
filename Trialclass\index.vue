<template>
  <page-meta :page-style="'overflow:' + (showTime ? 'hidden' : 'visible')"></page-meta>
  <view class="plr-20 pb-30" v-if="showPage">
    <view class="bg-ff plr-20 pb-40 radius-15 positionRelative">
      <form id="#nform">
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人姓名：</view>
          <view class="phone-input">
            <input type="text" @input="inputName" :value="infolist.expName" name="trialname" placeholder="请输入试课人姓名" class="input c-00" :disabled="isDisable || isEdit" />
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人年级：</view>
          <view class="phone-input ptb-5 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="14"></u-icon>
            </view>
            <uni-data-select
              v-if="isDisable"
              :disabled="true"
              :clear="false"
              class="uni-select c-99 w100"
              v-model="infolist.grade"
              :localdata="gradelist"
              @change="choice"
            ></uni-data-select>
            <uni-data-select v-if="!isDisable" class="uni-select c-99 w100" v-model="infolist.grade" :localdata="gradelist" @change="choice"></uni-data-select>
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">课程类型：</view>
          <view class="phone-input">
            <input type="text" :value="infolist.curriculumName" name="trialname" placeholder="请输入试课人姓名" class="input c-00" :disabled="true" />
          </view>
        </view>
        <view class="information ptb-15 borderB">
          <view style="width: 65%" class="f-30 bold">试课人性别：</view>
          <view class="phone-input ptb-5 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="14"></u-icon>
            </view>
            <uni-data-select
              v-if="isDisable"
              :disabled="true"
              :clear="false"
              class="uni-select c-99 w100"
              v-model="infolist.sex"
              :localdata="range"
              @change="choose"
            ></uni-data-select>
            <uni-data-select v-if="!isDisable" class="uni-select c-99 w100" v-model="infolist.sex" :localdata="range" @change="choose"></uni-data-select>
          </view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 73%" class="f-30 bold">试课人联系方式：</view>
          <view class="phone-input" style="padding-left: 0">
            <input
              type="number"
              @input="inputNumber"
              :value="infolist.expPhone"
              name="number"
              placeholder="请输入试课人联系方式"
              class="input c-00"
              maxlength="11"
              :disabled="isDisable || isEdit"
            />
          </view>
        </view>
        <view class="information ptb-30 borderB">
          <view style="width: 70%" class="f-30">
            <view class="bold">预约试课时间：</view>
            <view class="c-99 f-24" v-if="!isDisable">填写限制{{ infolist.expReservationHour || '' }}小时之后</view>
          </view>
          <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" v-if="isDisable">
            <view class="flex-a-c">
              <text class="uni-input c-00">{{ getShowTime(infolist.expectTime) }}</text>
            </view>
          </view>
          <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" @click="openTime" v-if="!isDisable">
            <view class="flex-a-c">
              <text style="float: left" :class="date == '' ? 'regions' : 'date_color'">{{ date == '' ? '请选择' : date }}</text>
            </view>
            <view class="time-icon">
              <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="14"></u-icon>
            </view>
            <!-- <u-datetime-picker ref="datetimePicker" :minDate="time" :show="showTime" v-model="datevalue" mode="datetime"
            itemHeight="68" confirmColor="#2e896f" @cancel="cancel"
            @confirm="bindDateChange" :immediateChange="true"></u-datetime-picker> -->

            <!-- <picker mode="date" class="f-30 w100 pl-10" :value="date" @change="bindDateChange"
            :disabled="isDisable" style="height: 100%;line-height:72rpx;">
            <text v-if="show" class="uni-input c-00">{{date}}</text>
            <text v-else
              :class="infolist.expectTime==undefined||infolist.expectTime=='' ?'regions':'date_color'">
              {{infolist.expectTime == undefined||infolist.expectTime=='' ? '请选择' : infolist.expectTime}}</text>
          </picker> -->
            <!-- <uni-icons v-if="isShow" type="bottom" size="14" color="#999"></uni-icons> -->
          </view>
        </view>

        <view class="information ptb-15 borderB">
          <view style="width: 73%" class="f-30 bold">试课人所在区域：</view>
          <view class="regions-input flex-s ptb-20 pr-20 positionRelative">
            <view class="icon_x" v-if="!isDisable">
              <u-icon name="arrow-right" color="#c7c7c7" size="14"></u-icon>
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <picker mode="region" class="f-30 lh-40 mt-5 w100 pl-8" @change="bindRegionChange" :disabled="isDisable" style="height: 100%">
              <text v-if="region" class="c-00">{{ region[0] }}{{ region[1] }}{{ region[2] }}</text>
              <text v-else :class="place == '' ? 'regions' : 'date_color'">{{ place == '' ? '请选择' : place }}</text>
            </picker>
            <!-- #endif -->

            <!-- #ifdef APP-PLUS -->
            <gb-picker @change="bindAppRegionChange" @tap="openPicker">
              <text v-if="region" class="f-28">{{ region[0] }}{{ region[1] }}{{ region[2] }}</text>
              <!-- <text v-else class="f-28 c-99">选择省、市、区</text> -->
              <text v-else :class="place == '' ? 'regions' : 'date_color'">{{ place == '' ? '请选择' : place }}</text>
            </gb-picker>
            <!-- #endif -->

            <uni-icons v-if="isShow" type="bottom" size="14" color="#999"></uni-icons>
          </view>
        </view>

        <view class="information ptb-15 borderB" v-if="infolist.curriculumName == '鼎英语'">
          <view style="width: 65%" class="f-30 bold">英语分数：</view>
          <view class="phone-input">
            <input type="text" @input="inputScore" :value="infolist.score" name="trialname" placeholder="请输入" class="input c-00" :disabled="isDisable" />
            <span class="ml-40 c-00">分</span>
          </view>
        </view>

        <view class="borderB pt-30 pb-25" v-if="infolist.curriculumName == '鼎英语'">
          <view class="flex-a-c f-30">
            <view class="bold" style="width: 60%" v-if="!isDisable">试课对象：</view>
            <view class="bold" style="width: 42%" v-else>试课对象：</view>
            <view v-if="!isDisable" class="w100 flex-a-c">
              <view class="flex-a-c mr-30">
                <uni-icons :type="current == 0 ? 'circle' : 'circle-filled'" :color="current == 0 ? '#999' : '#2e896f'" size="22" @click="changeCurrent(1)"></uni-icons>
                <span class="ml-15 c-66">B端</span>
                <!-- 是 -->
              </view>

              <view class="flex-a-c ml-40">
                <uni-icons :type="index == 0 ? 'circle' : 'circle-filled'" :color="index == 0 ? '#999' : '#2e896f'" size="22" @click="changeIndex(0)"></uni-icons>
                <span class="ml-15 c-66">C端</span>
                <!-- 否 -->
              </view>
            </view>
            <!-- 1 B端   2 C端 -->
            <view v-else>{{ infolist.experienceObject == 2 ? 'C端' : infolist.experienceObject == 1 ? 'B端' : '无' }}</view>
          </view>

          <view class="f-30 mt-10">
            <view v-if="!isDisable && current == 1" class="flex-a-c">
              <view class="bold" style="width: 65%">客户姓名：</view>
              <view class="phone-input">
                <input type="text" @input="inputClientName" :value="infolist.clientName" placeholder="请输入" class="input c-00" :disabled="isDisable" />
              </view>
            </view>
            <view v-if="isDisable && infolist.experienceObject == 1 && infolist.clientName && infolist.clientName != ''" class="flex-a-c mt-30 borderT pt-30">
              <view class="bold" style="width: 42%">客户姓名：</view>
              <view>{{ infolist.clientName }}</view>
            </view>
          </view>
        </view>

        <view class="flex-a-c ptb-15 borderB ptb-30 f-30">
          <view class="bold" style="width: 60%" v-if="!isDisable">咨询师：</view>
          <view class="bold" style="width: 42%" v-else>咨询师：</view>
          <view v-if="!isDisable" class="w100 flex-a-c">
            <view class="flex-a-c mr-30">
              <uni-icons
                :type="counselorOther == 0 ? 'circle' : 'circle-filled'"
                :color="counselorOther == 0 ? '#999' : '#2e896f'"
                size="22"
                @click="changeCounselorOther(1)"
              ></uni-icons>
              <span class="ml-15 c-66">上级推荐人</span>
              <!-- 是 -->
            </view>

            <view class="flex-a-c ml-40">
              <uni-icons
                :type="counselorSelf == 0 ? 'circle' : 'circle-filled'"
                :color="counselorSelf == 0 ? '#999' : '#2e896f'"
                size="22"
                @click="changeCounselorSelf(0)"
              ></uni-icons>
              <span class="ml-15 c-66">自己</span>
              <!-- 否 -->
            </view>
          </view>
          <view v-else>{{ infolist.counselor == '0' ? '自己' : infolist.counselor == '1' ? '上级推荐人' : infolist.counselor == '' ? '无' : infolist.counselor }}</view>
        </view>

        <view class="flex-a-c ptb-15 borderB ptb-30 f-30" v-if="infolist.curriculumName == '鼎英语'">
          <view class="bold" style="width: 60%" v-if="!isDisable">英语课外辅导：</view>
          <view class="bold" style="width: 42%" v-else>英语课外辅导：</view>
          <view v-if="!isDisable" class="w100 flex-a-c">
            <view class="flex-a-c mr-30">
              <uni-icons :type="radio == 0 ? 'circle' : 'circle-filled'" :color="radio == 0 ? '#999' : '#2e896f'" size="22" @click="changeRadio(1)"></uni-icons>
              <span class="ml-15 c-66">是</span>
            </view>

            <view class="flex-a-c ml-40">
              <uni-icons :type="val == 0 ? 'circle' : 'circle-filled'" :color="val == 0 ? '#999' : '#2e896f'" size="22" @click="changeVal(0)"></uni-icons>
              <span class="ml-15 c-66">否</span>
            </view>
          </view>
          <view v-else>{{ infolist.classInstruction == 0 ? '否' : '是' }}</view>
        </view>

        <view class="ptb-30" style="padding-bottom: 60rpx">
          <view class="f-30 bold">体验需求：</view>
          <view class="mt-30 p-30 bg-f7 radius-15" v-if="!isDisable">
            <textarea @input="inputRemark" v-model="remark" placeholder-style="color:#999" placeholder="请输入" :disabled="isDisable" />
          </view>

          <view class="mt-30" v-else>
            {{ infolist.remark }}
          </view>
        </view>

        <view class="tips" v-if="isShow" :style="{ height: svHeight + 'px' }">
          <button class="phone-btn" @click="getTrial()" :disabled="disabled">完成</button>
        </view>
      </form>
    </view>
    <uni-popup ref="dataShow" type="bottom" @maskClick="cancelAtion()">
      <view class="dialogBG ptb-20">
        <longDate ref="choseDate" chooseNum="14" @select="seletTime"></longDate>
        <view class="top-button">
          <button class="radius-50 cancel-button" @click="cancelAtion()">取消</button>
          <button class="radius-50 confirm-button" @click="confirm()">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 该时段预约已满，请选择其他时段 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-20 mlr-30" style="box-shadow: 0rpx 0rpx 20rpx #e0e0e0">
        <u-icon name="error-circle-fill" color="#FA370E" size="42"></u-icon>
        <view class="f-34 ml-15">{{ timeTipStr }}</view>
      </view>
    </uni-popup>
        <!-- 确认弹窗 -->
    <uni-popup ref="confirmPopup" type="center">
      <view class="t-c bg-ff ptb-25 radius-10 mt-20 mlr-30 p-25" style="width: 70%;margin: auto;">
        <view class="ptb-20">请确认试课人联系方式是否正确，若填写错误将无法上课</view>
        <view class="ptb-20" style="color: #FF4949;">{{number}}</view>
        <view class="top-button">
          <button class="radius-10 cancel-button" @click="secondCancel()" style="width: 180rpx;height: 60rpx;">去修改</button>
          <button class="radius-10 confirm-button" @click="getTrial(true)" style="width: 180rpx;height: 60rpx;">确定</button>
        </view>
      </view>
    </uni-popup>

    <!-- 省市区选择 province city area初始省市区设置 show:是否显示  @sureSelectArea：确认事件 @hideShow：隐藏事件-->
    <cc-selectDity :province="province" :city="city" :area="area" :show="showLocal" @changeClick="changeClick" @sureSelectArea="onsetCity" @hideShow="onhideShow"></cc-selectDity>
  </view>
  <!-- h5 -->
  <web-view :src="urlAddress" @message="handleMessage" v-if="showWebview"></web-view>
</template>

<script>
  const { $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  import Util from '@/util/util.js';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import dayjs from 'dayjs';
  import longDate from './components/long-date/long-date.vue';
  export default {
    components: {
      longDate
    },
    data() {
      const currentDate = this.getDate({
        format: true
      });
      return {
        urlAddress: '', // web-view url地址
        showWebview: false, // 是否显示web-view
        showPage: false, // 是否显示页面
        title: 'input',
        focus: false,
        inputValue: '',
        changeValue: '',
        mobile: '', // 推荐人手机号
        trialname: '', // 试课人姓名
        number: '', // 试课人手机号
        date: '', // 日期
        show: false, // 是否显示日期
        region: '',
        gender: '', //性别
        range: [
          {
            value: 1,
            text: '男'
          },
          {
            value: 2,
            text: '女'
          }
        ],
        grade: '', //年级
        infolist: {}, // 试课单回显信息
        place: '', // 区域

        gradelist: [],
        orderId: '',
        orderNo: '',
        payStatus: '', //1填写试课单   2查看试课单
        list: '',
        isShow: true, //是否显示按钮
        isDisable: false, //是否禁用
        useHeight: 0, //除头部之外高度

        svHeight: 50,

        imgHost: getApp().globalData.imgsomeHost,

        datevalue: '',
        startTime: '', // 开始时间
        showTime: false,
        time: '',
        index: 0, //
        current: 0, // 是否住校（是否需要咨询）

        radio: 0,
        val: 0,
        score: '', // 英语成绩
        residentialSchool: '', // 是否住校（是否需要咨询）
        classInstruction: '', // 英语课外辅导
        remark: '', // 体验需求
        flag: false, // 防止重复点击

        disabled: false,

        clientName: '', //客户姓名

        //咨询师选择
        counselor: '',
        counselorOther: 0,
        counselorSelf: 0,
        curriculumId: '',
        trialTimeData: [],
        normalDateBack: {},
        choseDateBack: {},
        sureChoseData: null,
        newDate: '', //新日期
        weekdaysShort: '周日_周一_周二_周三_周四_周五_周六'.split('_'),
        timeTipStr: '',
        isEdit: false,
        expReservationHour: 0,
        showLocal: false,
        province: '广东省',
        city: '广州市',
        area: '天河区',
        areaCode: '440106',
        app: 0
      };
    },
    onLoad(e) {
      //   console.log(e);
      // #ifdef APP-PLUS
      this.app = e.app;
      this.$handleTokenFormNative(e);
      // #endif
      // 1对多直接打开h5页面
      if (e.isOneToMany == 1) {
        this.showWebview = true;
        this.urlAddress = decodeURIComponent(e.url);
        console.log('🚀 ~ onLoad ~ this.urlAddress:', this.urlAddress);
        return;
      }
      this.showPage = true;
      this.orderId = e.orderId;
      this.payStatus = e.payStatus;
      this.curriculumId = e.curriculumId;
      this.isEdit = e.isEdit ? e.isEdit : false;
      //标题
      let title = '试课单';
      if (this.payStatus == 1) {
        if (this.isEdit) {
          title = '修改试课单';
        } else {
          title = '填写试课单';
        }
      }
      uni.setNavigationBarTitle({
        title: title
      });
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });

      // 微信小程序需要用此写法
      // this.$refs.datetimePicker.setFormatter(this.formatter)
    },

    onUnload() {
      // #ifdef APP-PLUS
      if (this.app) {
        plus.runtime.quit();
      }
      // #endif
    },

    onShow() {
      if (this.showWebview) {
        return;
      }
      this.init();
      this.getNowHour();
      if (this.payStatus == 1) {
        ///初始化时间选择
        this.normalDateBack = {
          timeWeek: this.getTimeWeek(),
          time: this.getTime2(),
          isRequest: true,
          valueArr: [0, 0]
        };
        this.seletTime(this.normalDateBack, true);
      }
    },

    methods: {
      getNowHour() {
        let that = this;
        $http({
          url: 'zx/exp/getInfo',
          data: {
            orderId: that.orderId
          }
        }).then(({ data }) => {
          that.expReservationHour = !!data.expReservationHour ? +data.expReservationHour : 24;
          uni.setStorageSync('expReservationHour', that.expReservationHour);
        });
      },
      ////NEW//试课时间/////Start////
      openTime() {
        let that = this;
        if (!this.isDisable) {
          this.$refs.dataShow.open();
          setTimeout(() => {
            this.getTime();
            this.showTime = true;
            let index = this.getCurHourIndex();
            //打开时间定位
            if (this.sureChoseData) {
              if (this.sureChoseData.valueArr && this.sureChoseData.valueArr.length != 0) {
                console.log(this.sureChoseData.valueArr);
                this.choseDateBack = this.sureChoseData;
                this.choseDateBack.isRequest = true;
                this.seletTime(this.choseDateBack, false, true);
                this.$refs.choseDate.getDataforChoseIndex(this.sureChoseData.valueArr);
              }
            } else {
              console.log('定位当前时间坐标2');
              this.choseDateBack = this.normalDateBack;
              this.choseDateBack.isRequest = true;
              this.seletTime(this.choseDateBack, false, true);
              this.$refs.choseDate.getDataforChoseIndex([0, index]);
            }
            let hour = uni.getStorageSync('expReservationHour') * 1;
            let sumHour = new Date().getHours() + hour;
            if (sumHour >= 24) {
              let day = parseInt(sumHour / 24);
              that.$refs.choseDate.initDate(day);
            } else {
              if (index == 0) {
                that.$refs.choseDate.initDate(1);
              } else {
                that.$refs.choseDate.initDate(0);
              }
            }
          });
        }
      },
      //修改查看试课单的时间
      getShowTime(date) {
        if (date) {
          let time = new Date(date);
          let dateTime = dayjs(time).format('MM月DD日');
          let week = dayjs(time).get('day');
          let hour = dayjs(time).get('hour');
          let minute = dayjs(time).get('minute');
          let endHour = dayjs(time).add(1, 'hour').get('hour');
          return `${dateTime} ${this.weekdaysShort[week]} ${this.addZero(hour)}:${this.addZero(minute)}~${this.addZero(endHour)}:${this.addZero(minute)}`;
        }
        return '';
      },
      addZero(data) {
        return data < 10 ? `0${data}` : data;
      },
      //当前时间 格式YYYY-MM-DD
      getTime2() {
        let nowDate = Date.now();
        let index = this.getCurHourIndex();
        let endTime = '';
        let hour = uni.getStorageSync('expReservationHour') * 1;
        let sumHour = new Date().getHours() + hour;

        if (sumHour >= 24) {
          if (index == 0) {
            let day = parseInt(sumHour / 24);
            endTime = dayjs(nowDate).add(day, 'day').format('YYYY-MM-DD');
          } else {
            endTime = dayjs(nowDate).add(hour, 'hour').format('YYYY-MM-DD');
          }
        } else {
          // endTime = dayjs(nowDate).add(hour, 'hour').format('YYYY-MM-DD');
          // console.log(endTime, 'endTimeendTimeendTimeendTimeendTime');
          // console.log(index, 'indexindexindexindexindexindexindexindex');
          if (index == 0) {
            endTime = dayjs(nowDate).add(1, 'day').format('YYYY-MM-DD');
            console.log(endTime, 'endTimeendTimeendTimeendTimeendTime');
          } else {
            endTime = dayjs(nowDate).add(hour, 'hour').format('YYYY-MM-DD');
            console.log(endTime, 'endTimeendTimeendTimeendTimeendTime');
          }
        }
        // console.log(nowDate, endTime, '***************************************');
        return endTime;
      },
      //当前时间 格式 月 日 周
      getTimeWeek() {
        let nowDate = Date.now();
        let index = this.getCurHourIndex();
        let endTime = '';
        let week = '';
        let hour = uni.getStorageSync('expReservationHour') * 1;
        let sumHour = new Date().getHours() + hour;
        if (sumHour >= 24) {
          if (index == 0) {
            let day = parseInt(sumHour / 24);
            endTime = dayjs(nowDate).add(day, 'day').format('MM月DD日');
            week = dayjs(nowDate).add(day, 'day').get('day');
            // endTime = dayjs(nowDate).add(1, 'day').format('MM月DD日');
            // week = dayjs(nowDate).add(1, 'day').get('day');
          } else {
            endTime = dayjs(nowDate).add(hour, 'hour').format('MM月DD日');
            week = dayjs(nowDate).add(hour, 'hour').get('day');
          }
        } else {
          // endTime = dayjs(nowDate).add(hour, 'hour').format('MM月DD日');
          // week = dayjs(nowDate).add(hour, 'hour').get('day');
          if (index == 0) {
            endTime = dayjs(nowDate).add(1, 'day').format('MM月DD日');
            week = dayjs(nowDate).add(1, 'day').get('day');
          } else {
            endTime = dayjs(nowDate).add(hour, 'hour').format('MM月DD日');
            week = dayjs(nowDate).add(hour, 'hour').get('day');
          }
        }
        return endTime + ' ' + this.weekdaysShort[week];
      },
      cancelAtion() {
        this.showTime = false;
        this.$refs.dataShow.close();
      },
      confirm() {
        if (!this.choseDateBack.hour) {
          this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
        }
        console.log(this.choseDateBack.hour);
        if (this.choseDateBack.hour.isNormal) {
          uni.showToast({
            icon: 'none',
            title: '时间段状态加载中~'
          });
          return;
        }
        // isSameOrAfter
        let hour = uni.getStorageSync('expReservationHour');
        if (!dayjs(this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime).isAfter(dayjs(Date.now()).add(hour, 'hour'))) {
          uni.showToast({
            icon: 'none',
            title: `请选择${hour}小时后的时间~`
          });
          return;
        }
        if (!this.choseDateBack.hour.canReserve) {
          uni.showToast({
            icon: 'none',
            title: '该时段预约已满，请选择其他时段'
          });
          // this.timeTipStr = "该时段预约已满，请选择其他时段"
          // this.$refs.notifyPopup.open();
          // setTimeout(()=>{
          // 	this.$refs.notifyPopup.close();
          // },1500)
          return;
        }
        // 确定后显示时间
        this.date = this.choseDateBack.timeWeek + ' ' + this.choseDateBack.hour.startTime + '~' + this.choseDateBack.hour.endTime;
        this.newDate = this.choseDateBack.time + ' ' + this.choseDateBack.hour.startTime;
        this.sureChoseData = this.choseDateBack;
        console.log(this.newDate);
        this.show = true;
        this.cancelAtion();
        this.$forceUpdate();
      },
      seletTime(val, isnormal, openPopup) {
        console.log(val);
        //请求数据接口
        this.choseDateBack = val;

        if (val.isRequest) {
          //默认数据用于定位日期
          this.getNormalData(isnormal, openPopup);
          this.getServiceData(isnormal, openPopup);
        } else {
          this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
        }
      },
      //请求状态
      async getServiceData(isnormal, openPopup) {
        // &curriculumId=${}
        let res = await this.$httpUser.get(`deliver/app/common/selUseExperienceUsableTime?date=${this.choseDateBack.time}&curriculumId=${this.curriculumId}`);
        if (res && res.data && res.data.data) {
          //
          let a = res.config.url.split('&')[0].split('date=')[1];
          if (a == this.choseDateBack.time) {
            this.trialTimeData = [];
            this.trialTimeData = res.data.data;
          }
          this.timeDataOpen(isnormal, openPopup);
        }
      },
      //默认数据
      getNormalData(isnormal, openPopup) {
        console.log('--getNormalData--');
        this.trialTimeData = [];
        for (let i = 8; i < 23; i++) {
          let data1 = {};
          data1.startTime = i < 10 ? `0${i}:00` : `${i}:00`;
          data1.endTime = i + 1 < 10 ? `0${i + 1}:00` : `${i + 1}:00`;
          data1.canReserve = false;
          data1.isNormal = true;
          let data2 = {};
          data2.startTime = i < 10 ? `0${i}:30` : `${i}:30`;
          data2.endTime = i + 1 < 10 ? `0${i + 1}:30` : `${i + 1}:30`;
          data2.canReserve = false;
          data2.isNormal = true;
          this.trialTimeData.push(data1);
          this.trialTimeData.push(data2);
        }
        this.timeDataOpen(isnormal, openPopup);
      },
      timeDataOpen(isnormal, openPopup) {
        let index = this.getCurHourIndex();
        if (this.choseDateBack.time == this.getTime2()) {
          this.trialTimeData = this.trialTimeData.slice(index, this.trialTimeData.length);
        }
        setTimeout(() => {
          console.log(this.$refs.choseDate);
          this.$refs.choseDate.getDataforChoseDate(this.trialTimeData);
        }, 30);
        if (isnormal) {
          let index = this.getCurHourIndex();
          this.choseDateBack.hour = this.trialTimeData[index];
          this.choseDateBack.valueArr[1] = index;
          this.normalDateBack.hour = this.trialTimeData[index];
          this.normalDateBack.valueArr[1] = index;
        } else {
          if (!openPopup) {
            this.choseDateBack.valueArr[1] = 0;
            this.choseDateBack.hour = this.trialTimeData[this.choseDateBack.valueArr[1]];
            this.$refs.choseDate.getDataforChoseIndex(this.choseDateBack.valueArr);
          }
        }
      },
      //定位到当前时间段
      getCurHourIndex() {
        let that = this;
        let today = new Date();
        let hour = uni.getStorageSync('expReservationHour') * 1;
        let currentHour = (today.getHours() + hour) % 24;
        // console.log(currentHour, today.getHours() + hour, 'currentHourcurrentHourcurrentHourcurrentHourcurrentHour');
        let currentMinute = today.getMinutes();
        let currentTime = [];
        if (currentMinute < 30) {
          //小于半小时 则hour 不变 min等于半小时
          currentTime = [currentHour, 30];
        } else {
          //大于等于半小时  则hour+1 min等于0
          currentTime = [currentHour + 1, 0];
        }
        for (let i = 0; i < this.trialTimeData.length; i++) {
          const [startHour, startMinute] = this.getStartTimeArr(this.trialTimeData[i].startTime);
          if (startHour == currentTime[0] && startMinute == currentTime[1]) {
            return i;
          }
        }
        return 0;
      },

      getStartTimeArr(time) {
        let timeArray = time.split(':');
        let hour = parseInt(timeArray[0]);
        let minute = parseInt(timeArray[1]);
        return [hour, minute];
      },
      ///NEW///试课时间/////End////

      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        if (type === 'day') {
          return `${value}日`;
        }
        if (type === 'hour') {
          return `${value}时`;
        }
        if (type === 'minute') {
          return `${value}分`;
        }
        return value;
      },
      getTime() {
        let nowDate = Date.now();
        // let setDate = dayjs(date).unix() * 1000;
        let endTime = dayjs(nowDate).format('YYYY-MM-DD HH:mm');
        let hour = uni.getStorageSync('expReservationHour') * 1;
        let endTimes = dayjs(nowDate).add(hour, 'hour');
        // let endTimes = dayjs(nowDate).add(1, 'day');
        this.time = dayjs(endTimes).valueOf();
        console.log(nowDate, endTime);
      },
      changeCurrent(value) {
        this.current = 1;
        this.index = 0;
        this.residentialSchool = value;
      },
      changeIndex(e) {
        this.residentialSchool = e;
        this.current = 0;
        this.index = 1;
      },
      changeRadio(value) {
        this.radio = 1;
        this.val = 0;
        this.classInstruction = value;
      },
      changeVal(e) {
        this.radio = 0;
        this.val = 1;
        this.classInstruction = e;
      },
      //咨询师
      changeCounselorOther(value) {
        this.counselorOther = 1;
        this.counselorSelf = 0;
        this.counselor = value;
      },
      changeCounselorSelf(e) {
        this.counselorOther = 0;
        this.counselorSelf = 1;
        this.counselor = e;
      },

      cancel() {
        this.showTime = false;
      },

      handleSubmit(e) {
        console.log(e);
        // {year: "2023", month: "07", day: "11", hour: "15", minute: "21", seconds: '55'}
        // this.birthday = `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${seconds}`;
      },

      async init() {
        await this.getGrade();
        if (this.payStatus == 2) {
          this.isShow = false;
          // this.$refs.trialLook.trialShow=true;
          this.getEchoinfo(true);
        } else {
          this.getEchoinfo(false);
        }
      },
      inputName(e) {
        this.trialname = e.detail.value;
      },
      inputNumber(e) {
        this.number = e.detail.value;
        // console.log(this.number);
      },
      inputScore(e) {
        this.score = e.detail.value;
      },
      inputClientName(e) {
        this.clientName = e.detail.value;
      },

      inputRemark(e) {
        this.remark = e.detail.value;
      },

      // 日期
      bindDateChange: function (e) {
        this.date = dayjs(e.value).format('YYYY-MM-DD HH:mm');
        console.log(this.date);
        this.show = true;
        this.showTime = false;
        this.$forceUpdate();
      },

      getDate(type) {
        const date = new Date();
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        if (type === 'start') {
          year = year - 60;
        } else if (type === 'end') {
          year = year + 2;
        }
        month = month > 9 ? month : '0' + month;
        day = day > 9 ? day : '0' + day;
        return `${year}-${month}-${day}`;
      },

      // 城市
      bindRegionChange: function (e) {
        this.region = e.detail.value;
      },
      bindAppRegionChange: function (e) {
        console.log(e);
        this.region = e.name;
      },
      openPicker() {
        console.log('执行打开地址选择器');
        this.showLocal = true;
      },
      changeClick(value, value2, value3, value4) {
        console.log('地址选择器 = ' + value + value2 + value3 + value4);
        this.province = value;
        this.city = value2;
        this.area = value3;
        this.areaCode = value4;
        // 更新 region 的值
        this.region = [value, value2, value3];
      },
      onhideShow() {
        this.showLocal = false;
        console.log('执行了关闭地址选择器');
      },
      // 选中省市区
      onsetCity(e) {
        let data = e.detail.target.dataset;
        let address = data.province + data.city + data.area;
        this.showLocal = false;
        // 更新 region 的值
        this.region = [data.province, data.city, data.area];
      },

      //性别下拉框
      choose(e) {
        this.gender = e;
      },
      //年级
      choice(e) {
        this.grade = e;
      },
      //年级
      async getGrade() {
        // let res = await this.$httpUser.get("znyy/bvstatus/GradeType");
        // if (res.data.success) {
        // 	let list = res.data.data
        // 	if (this.gradelist.length == 0) {
        // 		list.forEach((item) => this.gradelist.push({
        // 			value: Number(item.value),
        // 			text: item.label,
        // 			children: item.children,
        // 			ext: item.ext
        // 		}))
        // 	}
        // } else {
        // 	this.$util.alter(res.data.message)
        // }
        let list = [
          { value: '1', label: '一年级', ext: '', children: null },
          { value: '2', label: '二年级', ext: '', children: null },
          { value: '3', label: '三年级', ext: '', children: null },
          { value: '4', label: '四年级', ext: '', children: null },
          { value: '5', label: '五年级', ext: '', children: null },
          { value: '6', label: '六年级', ext: '', children: null },
          { value: '7', label: '七年级', ext: '', children: null },
          { value: '8', label: '八年级', ext: '', children: null },
          { value: '9', label: '九年级', ext: '', children: null },
          { value: '10', label: '高中一年级', ext: '', children: null },
          { value: '11', label: '高中二年级', ext: '', children: null },
          { value: '12', label: '高中三年级', ext: '', children: null },
          { value: '13', label: '大一', ext: '', children: null },
          { value: '14', label: '大二', ext: '', children: null },
          { value: '15', label: '大三', ext: '', children: null },
          { value: '16', label: '大四', ext: '', children: null },
          { value: '17', label: '其他', ext: '', children: null },
          { value: '18', label: '幼儿园', ext: '', children: null }
        ];
        if (this.gradelist.length == 0) {
          list.forEach((item) =>
            this.gradelist.push({
              value: Number(item.value),
              text: item.label,
              children: item.children,
              ext: item.ext
            })
          );
        }
      },
      secondCancel(){
        this.$refs.confirmPopup.close();
      },
      secondConfirm(){
        this.$refs.confirmPopup.open();
      },
      // 新增试课单
      async getTrial(confirm = false) {
        let _this = this;
        if (_this.flag) {
          return;
        }
        _this.flag = true;
        _this.disabled = true;
        if (_this.trialname == '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请输入姓名');
        }
        if (_this.grade == '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择年级');
        }
        if (_this.gender == '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择性别');
        }
        if (!Util.isMobile(_this.number)) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请输入试课人正确的联系方式');
        }
        if (_this.newDate == null || _this.newDate == '') {
          // if (_this.date == null) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择期望试课时间');
        }
        if (_this.region == null) {
          return $showError('请选择试课人所在区域');
        }
        if (this.infolist.curriculumName == '鼎英语') {
          if (_this.score == '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请输入英语成绩');
          }
          if (_this.classInstruction === '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择是否有英语课外辅导');
          }
          if (_this.residentialSchool === '') {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请选择试课对象');
          }
        }

        // 客戶姓名
        if (_this.residentialSchool === 1 && _this.clientName == '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请输入客户姓名');
        }
        if (_this.counselor === '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择咨询师');
        }

        let data = {
          orderId: _this.orderId,
          expName: _this.trialname,
          grade: _this.grade,
          sex: _this.gender,
          expPhone: _this.number,
          expectTime: _this.newDate,
          // expectTime: _this.date,
          province: _this.region[0],
          city: _this.region[1],
          area: _this.region[2],
          residentialSchool: '',
          remark: _this.remark,
          clientName: _this.clientName,
          curriculumId: _this.infolist.curriculumId,
          curriculumName: _this.infolist.curriculumName,
          counselor: _this.counselor,
          experienceObject: _this.residentialSchool ? 1 : 2
        };
        if (this.infolist.curriculumName == '鼎英语') {
          data.score = _this.score;
          data.classInstruction = _this.classInstruction;
        }
        let res = '';
        if (this.isEdit) {
          uni.showLoading();
          res = await $http({
            url: 'zx/exp/update',
            method: 'POST',
            data: data
          });
        } else {
          if(!confirm){
            _this.flag = false;
            _this.disabled = false;
            this.secondConfirm()
            return;
          }
          _this.secondCancel() // 关闭弹窗
          uni.showLoading();
          res = await $http({
            url: 'zx/exp/save',
            method: 'POST',
            data: data
          });
        }
        // let res = await $http({
        //   url: 'zx/exp/save',
        //   method: 'POST',
        //   data: data
        // });
        // _this.flag= false;
        if (res.status == 1) {
          // uni.navigateBack();
          uni.redirectTo({
            url: '/Coursedetails/tips/lessonTips?trailStatus=true'
          });
          /* 	uni.redirectTo({
          url: '/splitContent/officialAccount/staffCard?manageCode=123633&type=trialclass'
        }) */
          _this.flag = false;
          _this.disabled = false;
        } else {
          _this.flag = false;
          _this.disabled = false;
        }
        // uni.hideLoading();
      },

      // 试课单回显
      async getEchoinfo(isEdit) {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/exp/getInfo',
          data: {
            orderId: _this.orderId
          }
        });
        uni.hideLoading();
        if (res) {
          _this.isDisable = isEdit;
          if (res.data) {
            // 如果是一对多，则跳转到web页面
            if (res.data.isOneToMany) {
              var token = uni.getStorageSync('token');
              _this.orderNo = res.data.orderNo;
              _this.urlAddress = res.data.url + `?id=${_this.orderId || '{}'}&token=${token}&orderNo=${_this.orderNo}`;
              _this.showWebview = true;
              console.log('🚀 ~ getEchoinfo ~ _this.urlAddress:', _this.urlAddress);
            }
            // 正常流程
            if (_this.isEdit) {
              _this.infolist = res.data;
              _this.grade = res.data.grade;
              _this.gender = res.data.sex;
              _this.score = res.data.score;
              _this.remark = res.data.remark;
              _this.fristDate = _this.choseDateBack.timeWeek;
              // let time = _this.timeToMinutes(_this.choseDateBack.hour.startTime);
              // let dayStage = '';
              // if (time >= 480 && time < 720) {
              //   dayStage = '上午';
              // } else if (time >= 720 && time < 1020) {
              //   dayStage = '下午';
              // } else if (time >= 1020 && time < 1380) {
              //   dayStage = '晚上';
              // } else {
              //   dayStage = '';
              // }
              // _this.lastDate = `${dayStage} ${_this.choseDateBack.hour.startTime}~${_this.choseDateBack.hour.endTime}`;
              res.data.experienceObject ? _this.changeIndex(0) : _this.changeCurrent(1);
              res.data.classInstruction == 0 ? _this.changeVal(0) : _this.changeRadio(1);
              _this.counselor == 0 ? _this.changeCounselorSelf(0) : _this.changeCounselorSelf(1);
              _this.trialname = res.data.expName;
              _this.number = res.data.expPhone;
              _this.region = [_this.infolist.province, _this.infolist.city, _this.infolist.area];
              _this.place = _this.infolist.province + _this.infolist.city + _this.infolist.area;
            } else {
              _this.infolist = res.data;
              _this.trialname = res.data.expName;
              _this.number = res.data.expPhone;
              _this.place = _this.infolist.province + _this.infolist.city + _this.infolist.area;
            }
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
  }

  /deep/.date_color {
    color: #000 !important;
  }

  /deep/.regions {
    color: #999 !important;
    font-size: 30upx;
  }

  .regions-input {
    width: 100%;
    font-size: 30rpx;
    color: #999;
    display: flex;
    align-items: center;
  }

  /deep/.phone-btn {
    width: 586rpx;
    height: 80rpx;
    position: absolute;
    bottom: 40rpx;
    left: 54rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  /deep/.uni-select--disabled {
    background-color: #fff;
  }

  /deep/.uni-stat__select {
    height: 60rpx !important;
  }

  .borderB {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select__input-placeholder {
    color: #999 !important;
    font-size: 30rpx !important;
  }

  .icon_x {
    position: absolute;
    top: 28rpx;
    right: 0;
    z-index: 1;
  }

  .choose-icon2 {
    width: 35rpx;
    height: 35rpx;
  }

  .time-icon {
    /deep/.u-icon--right {
      position: absolute;
      right: 0;
      top: 20rpx;
    }
  }

  /deep/.u-picker__view {
    height: 600rpx !important;
  }

  .dialogBG {
    margin: 0 20rpx 20rpx 20rpx;
    height: 590rpx;
    background-color: #fff;
    border-radius: 12rpx;
  }

  .top-button {
    margin-top: 20rpx;
    text-align: center;
    height: 80rpx;
    display: flex;
    justify-content: space-evenly;
  }

  .confirm-button {
    width: 210rpx;
    height: 80rpx;
    background-color: #2e896f;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center; /* 文本水平居中对齐 */
    align-items: center; /* 文本垂直居中对齐 */
  }

  .cancel-button {
    width: 210rpx;
    height: 80rpx;
    border: 1px solid #2e896f;
    color: #2e896f;
    font-size: 32rpx;
    display: flex;
    justify-content: center; /* 文本水平居中对齐 */
    align-items: center; /* 文本垂直居中对齐 */
  }

  .borderT {
    border-top: 1px solid #efefef;
  }
</style>
