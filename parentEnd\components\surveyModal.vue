<template>
  <u-transition :show="showModal" :duration="1500">
    <view class="overlay" v-if="showModal">
      <view class="modal-container">
        <view class="modal">
          <view class="closeIcon">
            <image src="https://document.dxznjy.com/dxSelect/543422bb-9d24-4497-ace3-8b61037948e8.png" mode="" @click="closeDialog()"></image>
          </view>
          <view class="title">单词21天抗遗忘-AI模式用户调研</view>
          <view class="description">
            尊敬的用户，感谢您使用单词21天抗遗忘AI模式！为了持续优化服务质量，诚邀您对此次功能进行客观评价。评分采用5星制，点亮的星越多代表满意度越高！
          </view>
          <scroll-view scroll-y style="max-height: 80vh">
            <view class="content">
              <view class="question" v-for="(q, index) in questions" :key="index">
                <view class="text">{{ index + 1 }}. {{ q.text }}</view>
                <u-rate v-if="index != 5" count="5" v-model="q.score" active-color="#efc374" inactive-color="#e6e6e6" gutter="32" size="50"></u-rate>
                <u--textarea v-if="index == 5" v-model="q.answer" class="u-textarea" count :maxlength="200" placeholder="请输入您的建议" />
              </view>

              <view class="submit-btn-container">
                <view class="submit-btn" @click="submit">提交</view>
              </view>
            </view>
          </scroll-view>
          <!-- <view class="content">
            <view class="question" v-for="(q, index) in questions" :key="index">
              <view class="text">{{ index + 1 }}. {{ q.text }}</view>
              <u-rate v-if="index != 5" count="5" v-model="q.score" active-color="#efc374" inactive-color="#e6e6e6" gutter="32" size="50"></u-rate>
              <u--textarea v-if="index == 5" v-model="q.answer" class="u-textarea" count :maxlength="200" placeholder="请输入您的建议" />
            </view>

            <view class="submit-btn-container">
              <view class="submit-btn" @click="submit">提交</view>
            </view>
          </view> -->
        </view>
      </view>
    </view>
  </u-transition>
</template>

<script>
  export default {
    props: {
      showModal: {
        type: Boolean,
        default: false
      },
      useHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        // showModal: true,
        questions: [
          { text: '您认为AI模式的单词复习流程是否清晰易懂？', score: 0 },
          { text: 'AI模式在帮助您解决单词遗忘、巩固复习方面的效果如何？', score: 0 },
          { text: '在使用过程中，系统的响应速度是否达到您的预期？', score: 0 },
          { text: '在使用过程中，系统说明的中英文准确如何？', score: 0 },
          { text: '如果给AI模式整体评分，从功能得到内容呈现，您的整体体验感受如何？', score: 0 },
          { text: '您对AI模式在功能设计和使用体验上，还有哪些建议？', answer: '' }
        ]
      };
    },

    methods: {
      closeDialog() {
        this.$emit('closeDialog');
      },
      setScore(index, score) {
        this.questions[index].score = score;
      },
      getEnglish(num) {
        switch (num) {
          case 0:
            return 'One';
          case 1:
            return 'Two';
          case 2:
            return 'Three';
          case 3:
            return 'Four';
          case 4:
            return 'Five';
          case 5:
            return 'Six';
          default:
            return '';
        }
      },
      submit() {
        if (this.questions.some((q) => q.score == 0 && q.text != '您对AI模式在功能设计和使用体验上，还有哪些建议？')) {
          uni.showToast({
            title: '请先完成所有问题的评分',
            icon: 'none'
          });
          return;
        }
        if (this.questions[5].answer == '') {
          uni.showToast({
            title: '请填写您的建议',
            icon: 'none'
          });
          return;
        }
        const result = this.questions.map((q, index) => ({
          question: q.text,
          answer: index == '5' ? q.answer : q.score
        }));
        // let obj = {};
        // for (let index = 0; index < result.length; index++) {
        //   let str = this.getEnglish(index);
        //   obj[`question${str}`] = result[index].answer;
        // }
        // console.log('提交结果：', obj);
        // return;
        // this.$emit('confirm', obj);
        this.$emit('confirm', result);
      }
    }
  };
</script>

<style lang="scss">
  .overlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
  }
  .modal-container {
    padding: 32rpx;
  }
  .modal {
    width: 600rpx;
    max-height: 1200rpx;
    background: url('https://document.dxznjy.com/dxSelect/3d161f11-0242-4b28-b973-75efebcd8380.png') no-repeat center;
    background-position: top;
    background-size: contain;
    border-radius: 20rpx 20rpx 40rpx 40rpx;
    // position: relative;
    padding: 32rpx;
  }

  .closeIcon {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: sticky;
    z-index: 99;
    width: 100%;
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #21504b;
    text-align: center;
  }
  .description {
    text-indent: 2rem;
    font-size: 26rpx;
    color: #a2a6a4;
    line-height: 46rpx;
    text-align: left;
    margin-bottom: 32rpx;
  }
  .content {
    height: 800rpx;
    overflow-y: auto;
  }
  .question {
    margin-bottom: 32rpx;
    .text {
      font-size: 28rpx;
      color: #555555;
      line-height: 46rpx;
      margin-bottom: 24rpx;
    }
  }
  .stars {
    margin-top: 10rpx;
    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 32rpx;
    }
  }
  .star {
    font-size: 36rpx;
    margin-right: 10rpx;
    color: #ccc;
  }
  .star.active {
    color: #f5a623;
  }
  .textarea {
    margin-top: 10rpx;
    border: 1px solid #ccc;
    border-radius: 8rpx;
    padding: 10rpx;
    width: 100%;
    height: 100rpx;
  }
  .submit-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .submit-btn {
    background-color: #428a6f;
    color: #fff;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 50rpx;
    width: 500rpx;
    height: 50rpx;
    line-height: 50rpx;
    margin-top: 30rpx;
    font-size: 30rpx;
  }
</style>
