<template>
  <u-transition :show="showModal" :duration="1500">
    <view class="overlay" v-if="showModal">
      <view class="modal-container">
        <view class="modal">
          <view class="" style="margin-top: 90rpx">
            <view class="closeIcon">
              <image src="https://document.dxznjy.com/course/1747636501000" mode="" @click="closeDialog()"></image>
            </view>
            <!-- <view class="title">课时规划单调研</view> -->
            <view class="description">尊敬的用户，感谢您使用AI课程规划报告！为了持续优化服务质量，诚邀您对此次功能进行客观评价。评分采用5星制，点亮的星越多代表满意度越高！</view>
            <scroll-view scroll-y style="max-height: 80vh">
              <view class="content">
                <view class="question" v-for="(q, index) in questions" :key="index">
                  <view class="text">{{ index + 1 }}. {{ q.text }}</view>
                  <uni-rate
                    v-if="index != 3"
                    activeColor="#efc374"
                    color="#e6e6e6"
                    margin="15"
                    size="30"
                    :is-fill="false"
                    class="mt-40"
                    @change="(value) => changeScore(value, index)"
                  ></uni-rate>
                  <view class="textarea_style_css">
                    <textarea v-if="index == 3" v-model="q.answer" class="u-textarea" count maxlength="200" placeholder="请输入您的建议"></textarea>
                    <view v-if="index == 3" class="textarem_number f-24 c-55">{{ q.answer.length ? q.answer.length : 0 }}/200</view>
                  </view>
                </view>

                <view class="submit-btn-container">
                  <view class="submit-btn" @click="submit">提交</view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
  </u-transition>
</template>

<script>
  export default {
    props: {
      showModal: {
        type: Boolean,
        default: false
      },
      useHeight: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        // showModal: true,
        questions: [
          { text: '本次规划单对您的实际帮助程度如何？', score: 0 },
          { text: '规划单的逻辑推导与流程是否流畅清晰？', score: 0 },
          { text: '从功能操作到内容呈现，您的综合体验感受如何？', score: 0 },
          { text: '为进一步提升规划单的智能匹配精准度与实用性，期待您分享宝贵的改进建议：', answer: '' }
        ]
      };
    },

    methods: {
      closeDialog() {
        this.$emit('closeDialog');
      },
      setScore(index, score) {
        this.questions[index].score = score;
      },
      changeScore(value, index) {
        this.questions[index].score = value.value;
      },
      submit() {
        if (this.questions.some((q) => q.score == 0 && q.text != '您对AI模式在功能设计和使用体验上，还有哪些建议？')) {
          uni.showToast({
            title: '请先完成所有问题的评分',
            icon: 'none'
          });
          return;
        }
        if (this.questions[3].answer == '') {
          uni.showToast({
            title: '请填写您的建议',
            icon: 'none'
          });
          return;
        }
        const result = this.questions.map((q, index) => ({
          question: q.text,
          answer: index == '3' ? q.answer : q.score
        }));
        // let obj = {};
        // for (let index = 0; index < result.length; index++) {
        //   let str = this.getEnglish(index);
        //   obj[`question${str}`] = result[index].answer;
        // }
        // console.log('提交结果：', obj);
        // return;
        // this.$emit('confirm', obj);
        this.$emit('confirm', result);
      }
    }
  };
</script>

<style lang="scss">
  .overlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
  }
  .modal-container {
    padding: 32rpx;
  }
  .modal {
    width: 600rpx;
    max-height: 1200rpx;
    background: url('https://document.dxznjy.com/dxSelect/63af113b-039f-494f-aac7-86d7aa9c25c9.png') no-repeat center;
    background-position: top;
    background-size: cover;
    border-radius: 20rpx 20rpx 40rpx 40rpx;
    // position: relative;
    padding: 32rpx;
  }

  .closeIcon {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: sticky;
    z-index: 99;
    padding: 0 15rpx;
    width: 100%;
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    color: #21504b;
    text-align: center;
  }
  .description {
    text-indent: 2rem;
    font-size: 26rpx;
    color: #a2a6a4;
    line-height: 46rpx;
    text-align: left;
    margin: 32rpx;
  }
  .content {
    height: 800rpx;
    overflow-y: auto;
  }
  .question {
    margin-bottom: 32rpx;
    .text {
      font-size: 28rpx;
      color: #555555;
      line-height: 46rpx;
      margin-bottom: 44rpx;
    }
  }
  .stars {
    margin-top: 10rpx;
    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 32rpx;
    }
  }
  .star {
    font-size: 36rpx;
    margin-right: 10rpx;
    color: #ccc;
  }
  .star.active {
    color: #f5a623;
  }
  .u-textarea {
    border: 1px solid #ccc;
    border-radius: 8rpx;
    padding: 10rpx;
    background-color: #fff;
    width: 95%;
    height: 120rpx;
  }
  .textarea_style_css {
    position: relative;
  }
  .textarem_number {
    position: absolute;
    bottom: 0;
    right: 20rpx;
  }
  .submit-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .submit-btn {
    background-color: #428a6f;
    color: #fff;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 50rpx;
    width: 500rpx;
    height: 50rpx;
    line-height: 50rpx;
    margin-top: 30rpx;
    font-size: 30rpx;
  }
</style>
