<template>
  <view class="">
    <u-navbar title=" " :safeAreaInsetTop="true" placeholder>
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="14"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center">复习单词</view>
      </view>
      <view class="u-nav-slot1" slot="right" @click="goSetting">
        <u-icon name="setting" color="#000" bold size="15"></u-icon>
        <view class="nav-setting-text">设置</view>
      </view>
    </u-navbar>
    <!-- #ifdef MP-WEIXIN -->
    <view v-if="isShow">
      <page-container :show="isShow" :overlay="false" @beforeleave="beforeleave"></page-container>
    </view>
    <!-- #endif -->
    <view class="main-content plr-30 t-c" v-show="!toastShow">
      <view class="topicToastBottom-box" v-if="showTopic">
        <view class="chat-bubble">
          <view class="arrow arrow-top"></view>
          <view class="bubble-content">默认为手动复习，可在设置中切换为自动复习，单词录音发音结束后，自动复习下一个单词</view>
        </view>
      </view>
      <view class="page-content" :style="{ height: contentHeight + 'rpx' }">
        <swiper
          :style="{ height: swiperHeight + 'rpx', background: '#fff', padding: '0 20rpx', borderRadius: '20rpx' }"
          @change="swiperChange"
          :disable-touch="isDisabled"
          :swiperDuration="duration"
          :current="originIndex"
          :class="disabled ? 'locked-swiper' : 'app-swiper'"
          :interval="interval"
          @touchend="stopTouch"
        >
          <swiper-item v-for="(itemParent, indexParent) in originList" :key="indexParent" :catchtouchmove="isDisabled ? '任意非空字符' : ''">
            <view class="top">
              <view class="f-30 flex-a-c">
                <text style="color: #555555">{{ originIndex + 1 }}/{{ originList.length }}</text>
                <text style="color: #555555" class="ml-10 f-24">(已完成{{ reviewList.length }}个)</text>
              </view>
              <view class="flex-a-c">
                <u-icon name="reload" color="#428a6f" size="20" @click="refreshFn"></u-icon>
                <view class="mlr-10" style="border: 1px solid #ccc; height: 20rpx"></view>
                <view class="toast" style="color: #428a6f" @click="finishFn">结束复习</view>
              </view>
            </view>

            <view class="swiper-content" :style="{ height: swiperContentHeight + 'rpx' }">
              <view class="word">
                <view class="toast" v-if="configData.autoReviewNext == 1">每个单词会播放{{ configData.playbackCount || 1 }}遍</view>
                <!-- <view class="english mtb-12" style="color: #303133; line-height: 69rpx">{{ displayData.word || '' }}</view> -->
                <!-- <view :class="['chinese mtb-12', showChineseClass ? '' : 'blur']">{{ displayData.translation || '' }}</view> -->
                <view class="english mtb-12" style="color: #303133; line-height: 69rpx">
                  {{ itemParent.word || '' }}
                </view>
                <!-- <view :class="['chinese', showChineseClass ? '' : 'blur']">{{ itemParent.translation || '' }}</view> -->
                <view class="start-talking mt-55" @click="notKonw" :hover-class="'button-hover'" :hover-stay-time="150">
                  <view class="start-talk flex-a-c">不会</view>
                </view>
              </view>
              <!--          <view class="topicToastBottom-box" v-show="showTopic1">
                <view class="topicToastBottom">长按录音按钮开始录音，请说出单词中英文</view>
                <view class="" style="height: 100rpx; border: 1px solid #626262"></view>
              </view> -->
              <view class="finger" v-show="configData.autoReviewNext != 1 && fingleShow">
                <view class="title">左滑下一个</view>
                <image src="https://document.dxznjy.com/course/6baa281714604e54b7f5de7f5f54236f.png" style="width: 60rpx; height: 80rpx" mode=""></image>
              </view>
              <view class="chat-bubble" v-if="originIndex == 0 && showTopic1">
                <view class="bubble-content">长按录音按钮开始录音,请先说一次单词英文,再说一次单词中文,松开提交</view>
                <view class="arrow arrow-down"></view>
              </view>
              <view class="footer">
                <view class="footer_button" :hover-class="'record-button-hover'" :hover-stay-time="100">
                  <view
                    class="start-talk flex-a-c"
                    @longpress="startTalkLong"
                    @click="talkingShort"
                    @touchmove.stop.prevent="audioTouchmove"
                    @touchend.stop.prevent="audioTouchendShow = true"
                  >
                    按住录音
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <!-- 语音组件 -->
      <chatAudio @submit="submitAudio" :audioXY="audioXY" :audioShow="audioShow" :audioTouchendShow="audioTouchendShow" @closeAudioShow="closeAudio"></chatAudio>
      <u-overlay :show="overlayShow" :opacity="0.8">
        <view class="warp">
          <view class="rect">
            <view class="text">复习即将开始</view>
            <view class="text">请集中注意力哦</view>
            <view class="number">{{ times }}</view>
          </view>
        </view>
      </u-overlay>
      <u-modal :show="modalShow" title="由于您改变了播音类型，是否重新？" showCancelButton>
        <view class="slot-content">
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“允许”</text>
            将会用修改后的播音类型复习；
          </view>
          <view class="modal-title">
            点击
            <text style="color: #428a6f">“不允许”</text>
            将会用原播音类型继续复习，
          </view>
          <view class="modal-title">修改后的播音类型下次复习生效。</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeConfig(1)">允许</view>
        </view>
      </u-modal>

      <u-modal :show="voiceShow" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">“单词抗遗忘“</view>
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">想访问您的麦克风</view>
          <view class="modal-title">使用麦克风发音复习和指令控制</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeVoiceConfig(0)">不允许</view>
          <view class="btn confirm" @click="changeVoiceConfig(1)">允许</view>
        </view>
      </u-modal>
      <u-modal :show="backToast" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">确认结束复习吗</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeBackConfig(0)">结束复习</view>
          <view class="btn confirm" @click="changeBackConfig(1)">继续复习</view>
        </view>
      </u-modal>
      <u-modal :show="finishToast" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">
            已完成{{ reviewList.length }}个,剩余{{ originList.length - reviewList.length }}个，确认结束复习吗
          </view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn confirm" @click="changeFinishConfig(1)">继续复习</view>
          <view class="btn cancel" @click="changeFinishConfig(0)">结束复习</view>
        </view>
      </u-modal>
      <u-modal :show="forgetToast" title=" " showCancelButton>
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 28rpx; margin-bottom: 15rpx">您有{{ unKonwList.length }}个单词标记为不会，是否进行复习强化</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeForgetConfig(0)">否</view>
          <view class="btn confirm" @click="changeForgetConfig(1)">是</view>
        </view>
      </u-modal>
      <u-modal :show="refeshToast" title=" ">
        <view class="slot-content">
          <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">刷新页面后,需重新进行录音操作</view>
        </view>
        <view class="confirmButton" slot="confirmButton">
          <view class="btn cancel" @click="changeRefeshConfig(0)">取消</view>
          <view class="btn confirm" @click="changeRefeshConfig(1)">确定</view>
        </view>
      </u-modal>
    </view>
    <view class="overlay" :style="{ height: contentHeight + 'rpx' }" v-show="toastShow" @click="startFn">
      <image src="https://document.dxznjy.com/course/e9c3e15659274760b6720e37e1400916.png" class="overlay-image" mode="heightFix"></image>
    </view>
  </view>
</template>

<script>
  import chatAudio from './component/chatAudio.vue';
  // #ifdef MP-WEIXIN
  import sensors from 'sa-sdk-miniprogram';
  // #endif
  import CryptoJS from 'crypto-js';
  const { $http } = require('@/util/methods.js');
  var innerAudioContext;
  export default {
    components: {
      chatAudio
    },
    data() {
      return {
        isShow: true,
        useHeight: 0,
        studentCode: '',
        rollShow: false, //禁止滚动穿透,
        isRight: false,
        isFalse: false,
        useHeight: 0,
        displayData: {}, // swiper需要的数据
        originIndex: 0, // 记录源数据的下标
        nextIndex: 0, // 记录上次允许的位置
        // 源数据
        reviewList: [],
        originList: [],
        duration: 300,
        interval: 10000,
        overlayShow: false,
        toastShow: false,
        times: 3,
        countDown: null,
        showChinese: false,
        showChineseClass: false,
        configData: {},
        newConfigData: {},
        oldConfigData: {},
        isplay: false,
        audioXY: {
          x: 0,
          y: 0
        },
        audioShow: false, // 录制音频弹窗
        audioTouchendShow: false, // 是否结束录制音频
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: '',
        userCode: '',
        sg: '',
        batchId: null,
        disabled: false,
        promptPopup: true,
        modalShow: false,
        voiceShow: false,
        backToast: false,
        finishToast: false,
        forgetToast: false,
        isEnd: false,
        isDisabled: false,
        wordCount: 0,
        wordList: [],
        refeshToast: false,
        firstRefresh: true,
        isFirst: true,
        showTopic: false,
        showTopic1: true,
        unKonwList: [],
        fingleShow: false,
        statusBarHeight: 0,
        safeAreaBottom: 0,
        // App端固定高度
        fixedSwiperHeight: 1200
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;

          // 获取状态栏高度和安全区域信息
          that.statusBarHeight = (res.statusBarHeight || 0) * (750 / res.windowWidth);

          // #ifdef APP-PLUS
          // App端获取安全区域信息
          that.safeAreaBottom = res.safeAreaInsets ? res.safeAreaInsets.bottom * (750 / res.windowWidth) : 0;
          // #endif

          // #ifdef MP-WEIXIN
          // 微信小程序获取安全区域信息
          that.safeAreaBottom = res.safeArea ? (res.screenHeight - res.safeArea.bottom) * (750 / res.windowWidth) : 0;
          // #endif
        }
      });
    },

    onLoad(e) {
      let that = this;
      if (e) {
        that.studentCode = e.studentCode;
        that.batchId = e.batchId;
        that.wordCount = e.wordCount;
        // this.getConfigDetail()
        that.isFirst = uni.getStorageSync('isFirstAi');
        that.toastShow = uni.getStorageSync('isFirstAi');
        that.showTopic = uni.getStorageSync('isFirstAi');
        // that.showTopic1 = uni.getStorageSync('isFirstAi');
        that.firstRefresh = uni.getStorageSync('isFirstAi');
      }
      uni.getStorage({
        key: 'aiWordData',
        success(res) {
          that.originList = res.data;
          that.displayData = that.originList[0];
        }
      });
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        console.log('开始播放');
      });
      innerAudioContext.onStop(function () {
        console.log('播放结束');
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        console.log('播放暂停');
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
        that.isplay = false;
      });
      this.toastShow = true;
      // setTimeout(() => {
      //   this.countDomnFn();
      // }, 100);
    },
    onUnload() {
      this.configData = {};
      uni.setStorageSync('isFirstAi', false);
    },
    onHide() {
      uni.setStorageSync('isFirstAi', false);
    },
    onShow() {
      this.checkConfig();
      this.checkMicrophonePermission();
      this.homeData();
      this.getWordversion();
    },

    // #ifdef APP-PLUS
    // App端返回按键处理
    onBackPress() {
      this.goBack();
      return true; // 阻止默认返回行为
    },
    // #endif
    beforeDestroy() {
      if (this.countDown) {
        // 组件销毁时清除计时器
        clearInterval(this.countDown);
        this.countDown = null;
      }
    },
    computed: {
      eventHandler() {
        return this.isDisabled ? '666' : '';
      },
      // 内容区域高度计算
      contentHeight() {
        // #ifdef APP-PLUS
        // App端需要考虑状态栏和安全区域
        return this.useHeight - 200 - this.safeAreaBottom;
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 230;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 200;
        // #endif
      },
      // Swiper高度计算
      swiperHeight() {
        // #ifdef APP-PLUS
        // App端使用屏幕高度的70%，但不超过1400rpx，不少于1000rpx
        const calculatedHeight = this.useHeight * 0.7;
        return Math.max(1000, Math.min(1400, calculatedHeight));
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 280;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 250;
        // #endif
      },
      // Swiper内部内容高度计算
      swiperContentHeight() {
        // #ifdef APP-PLUS
        // App端内容高度为swiper高度减去顶部区域
        return this.swiperHeight - 150;
        // #endif

        // #ifdef MP-WEIXIN
        // 微信小程序
        return this.useHeight - 500;
        // #endif

        // #ifdef H5
        // H5端
        return this.useHeight - 500;
        // #endif
      }
    },
    methods: {
      notKonw() {
        let that = this;
        let arr = that.reviewList.filter((i) => i.word == that.displayData.word);
        if (arr && arr.length > 0) {
          that.reviewList = that.reviewList.filter((i) => i.word != that.displayData.word);
        }
        let arr1 = that.unKonwList.filter((i) => i.word == that.displayData.word);
        if (arr1 && arr1.length > 0) {
          that.unKonwList = that.unKonwList.filter((i) => i.word != that.displayData.word);
        }
        let arr2 = that.wordList.filter((i) => i.word == that.displayData.word);
        if (arr2 && arr2.length > 0) {
          that.wordList = that.wordList.filter((i) => i.word != that.displayData.word);
        }
        let model = {
          word: that.displayData.word,
          scheduleCode: that.displayData.scheduleCode,
          correct: 0,
          translation: that.displayData.translation,
          lastReviewCycle: that.displayData.lastReviewCycle,
          lastReviewDate: that.displayData.lastReviewDate,
          lastStudyTime: that.displayData.lastStudyTime,
          nextReviewDate: that.displayData.nextReviewDate,
          nowRound: that.displayData.lastReviewCycle
        };
        let unkonwObj = {
          audioUrl: '',
          word: that.displayData.word,
          chinese: that.displayData.translation,
          scheduleCode: that.displayData.scheduleCode,
          nowRound: that.displayData.lastReviewCycle,
          type: 0
        };
        let obj = { ...that.displayData };
        that.unKonwList.push(obj);
        that.wordList.push(unkonwObj);
        that.reviewList.push(model);
        uni.showToast({
          title: '已记录,后续可以进行强化复习',
          icon: 'none',
          complete: () => {
            if (that.configData.autoReviewNext == 1) {
              that.originIndex++;
            }
          }
        });
      },
      closeTopic() {
        this.showTopic = false;
      },
      beforeleave() {
        let that = this;
        that.isShow = false; //这个很重要，一定要先把弹框删除掉
        if (that.reviewList.length < 1) {
          // uni.navigateBack();
          uni.navigateBack({
            delta: 3
          });
          // that.backToast = true;
        } else {
          that.finishToast = true;
        }
      },
      goBack() {
        let that = this;
        innerAudioContext.stop();
        if (that.reviewList.length < 1) {
          // uni.navigateBack();
          uni.navigateBack({
            delta: 3
          });
        } else {
          that.finishToast = true;
        }
      },
      changeRefeshConfig(type) {
        if (type == 1) {
          this.refresh();
          this.refeshToast = false;
          this.firstRefresh = false;
        } else {
          this.refeshToast = false;
        }
      },
      refreshFn() {
        innerAudioContext.stop();
        if (this.firstRefresh) {
          this.refeshToast = true;
        } else {
          this.refresh();
        }
      },
      refresh() {
        this.reviewList = this.reviewList.filter((item) => item.word != this.displayData.word);

        this.wordList = this.wordList.filter((item) => item.word != this.displayData.word);

        this.clearStatus();
        this.playVoice();
      },
      finishFn() {
        innerAudioContext.stop();
        if (this.reviewList.length > 0) {
          this.finishToast = true;
        } else {
          uni.redirectTo({
            url: `/antiAmnesia/review/allWords?studentCode=${this.studentCode}`
          });
        }
      },
      async validateFn() {
        let that = this;
        uni.showLoading({ title: '提交中' });
        let queryData1 = {
          wordList: that.wordList,
          batchId: that.batchId,
          studentCode: that.studentCode
        };

        console.log(JSON.stringify(queryData1), 'JSON.stringify(queryData)JSON.stringify(queryData1)');
        let res = await that.$httpUser.post('znyy/word/review/validate', JSON.stringify(queryData1));
        if (res && res.data.success) {
          uni.hideLoading();
          return res.data.data;
        } else {
          uni.hideLoading();
          uni.showToast({
            title: 'AI系统繁忙,请稍后重试',
            icon: 'none'
          });
          return false;
        }
      },
      async finishReview() {
        let that = this;
        if (that.unKonwList.length > 0) {
          that.forgetToast = true;
        } else {
          let str = await that.validateFn();
          // let arr = str ? str.split('-') : [];
          if (str) {
            // let reportId = arr[1];
            let reportId = str;
            that.createReport(reportId);
          } else {
            return false;
          }
        }
      },
      async createReport(reportId) {
        let that = this;
        // let str = await that.validateFn();
        // let arr = str ? str.split('-') : [];

        // return console.log(str);
        // let correctNum = that.reviewList.filter((i) => i.correct == 1);
        // console.log(correctNum.length, 'correctNumcorrectNumcorrectNumcorrectNumcorrectNum');
        // var rate = Math.round((parseInt(correctNum.length) / that.reviewList.length) * 10000) / 100.0;
        let startTime = uni.getStorageSync('aiReviewStartTime');
        let endTime = new Date(Date.now()).getTime();
        let reviewDuration = parseInt((endTime - startTime) / 1000);
        if (that.reviewList.length <= 0) {
          that.$util.alter('未复习任何单词，不能提交！');
        } else {
          uni.showLoading({ title: '提交中' });
          let queryData = {
            reviewDuration: reviewDuration,
            reviewType: 1,
            batchId: that.batchId,
            studentCode: that.studentCode,
            reviewWordVM: that.reviewList,
            rate: '-1',
            reportId: reportId
          };
          let emitData = {
            wordTotalCount: that.wordCount,
            reviewTotal: that.reviewList.length,
            noReviewNum: that.wordCount - that.reviewList.length,
            forgetNum: 0
            // forgetNum: that.reviewList.length - correctNum.length
          };
          console.log(queryData, 'queryData,queryData,queryData');
          that.$httpUser.post('znyy/review/fun/review/mark/word', queryData).then((result) => {
            if (!result.data.success) {
              uni.hideLoading();
              that.$util.alter(result.data.message);
            } else {
              uni.hideLoading();
              uni.redirectTo({
                url: `/parentEnd/report/aiReviewReport?reviewId=${result.data.data}&history=0&studentCode=${that.studentCode}&showData=${encodeURIComponent(
                  JSON.stringify(emitData)
                )}`
              });
            }
          });
        }
      },
      async onlyCreateReport(reportId) {
        let that = this;
        // await that.validateFn();
        // let str = await that.validateFn();
        // return console.log(str);
        // let correctNum = that.reviewList.filter((i) => i.correct == 1);
        // console.log(correctNum.length, 'correctNumcorrectNumcorrectNumcorrectNumcorrectNum');
        // var rate = Math.round((parseInt(correctNum.length) / that.reviewList.length) * 10000) / 100.0;
        let startTime = uni.getStorageSync('aiReviewStartTime');
        let endTime = new Date(Date.now()).getTime();
        let reviewDuration = parseInt((endTime - startTime) / 1000);
        if (that.reviewList.length <= 0) {
          that.$util.alter('未复习任何单词，不能提交！');
        } else {
          uni.showLoading({ title: '提交中' });
          let queryData = {
            reviewDuration: reviewDuration,
            reviewType: 1,
            batchId: that.batchId,
            studentCode: that.studentCode,
            reviewWordVM: that.reviewList,
            rate: '-1',
            reportId: reportId
          };
          let emitData = {
            wordTotalCount: that.wordCount,
            reviewTotal: that.reviewList.length,
            noReviewNum: that.wordCount - that.reviewList.length,
            // forgetNum: that.reviewList.length - correctNum.length
            forgetNum: 0
          };
          that.$httpUser.post('znyy/review/fun/review/mark/word', queryData).then((result) => {
            if (!result.data.success) {
              uni.hideLoading();
              that.$util.alter(result.data.message);
            } else {
              uni.hideLoading();
              // 存储数据
              uni.setStorage({
                key: 'aiForgetWordData',
                data: that.unKonwList,
                success() {
                  uni.redirectTo({
                    url: `/antiAmnesia/review/aiForgetReview?reviewId=${result.data.data}&studentCode=${that.studentCode}&showData=${encodeURIComponent(JSON.stringify(emitData))}`
                  });
                }
              });
            }
          });
        }
      },
      isEqual(obj1, obj2) {
        // 如果类型不同，直接返回 false
        if (typeof obj1 !== typeof obj2) return false;
        // 如果是基本类型，直接比较值
        if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
          return obj1 === obj2;
        }
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // 检查属性数量是否相同
        if (keys1.length !== keys2.length) return false;

        // 检查每个属性的值是否严格相等
        for (const key of keys1) {
          if (obj1[key] !== obj2[key]) return false;
        }

        return true;
      },
      async checkConfig() {
        let that = this;
        let newConfigData = uni.getStorageSync('aiConfigData');
        let oldConfigData = JSON.parse(JSON.stringify(that.configData));
        if (oldConfigData.id) {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData111111111111');
          if (that.isEqual(oldConfigData, newConfigData)) {
            return;
          } else {
            that.modalShow = true;
          }
        } else {
          console.log(oldConfigData, 'oldConfigDataoldConfigDataoldConfigDataoldConfigData22222222222222');
          let res = await this.$httpUser.get('znyy/word/review/play-config', {
            studentCode: this.studentCode
          });
          if (res && res.data.success) {
            const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
            oldConfigData = JSON.parse(JSON.stringify(converted));
            that.configData = JSON.parse(JSON.stringify(converted));
            // that.showChinese = that.configData.showChinese == 1 ? true : false;
            // if (!that.showChinese) {
            //   that.showChineseClass = false;
            // }
            // that.promptPopup = that.configData.promptPopup == 1 ? true : false;
            // that.isFirst = that.configData.isFirst;
          }
        }
      },
      async getConfigDetail() {
        let res = await this.$httpUser.get('znyy/word/review/play-config', {
          studentCode: this.studentCode
        });
        if (res && res.data.success) {
          const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
          this.configData = converted;
          // this.showChinese = this.configData.showChinese == 1 ? true : false;
          // this.promptPopup = this.configData.promptPopup == 1 ? true : false;
          // this.isFirst = this.configData.isFirst;
          // if (!this.showChinese) {
          //   this.showChineseClass = false;
          // }
        }
      },
      changeConfig(val) {
        if (val == 1) {
          let data = uni.getStorageSync('aiConfigData');
          this.configData = JSON.parse(JSON.stringify(data));
          // this.showChinese = this.configData.showChinese == 1 ? true : false;
          // this.promptPopup = this.configData.promptPopup == 1 ? true : false;
          // this.isFirst = this.configData.isFirst;
          // if (!this.showChinese) {
          //   this.showChineseClass = false;
          // }
          this.modalShow = false;
        } else {
          this.modalShow = false;
        }
      },
      startFn() {
        this.toastShow = false;
        setTimeout(() => {
          this.countDomnFn();
        }, 100);
      },
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          let data = _this.userinfo.userCode + 'L0anhf';
          this.sg = CryptoJS.SHA1(data).toString();
        }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      sayWord(word) {
        var that = this;
        word = word.replace('/\g', '');
        word = word.replace('?\g', '');
        that.linkUrl = '';
        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              // let url;
              console.log(that.playType, '=================');
              // if (result.data.data.includes('http')) {
              //   voiceUrl = result.data.data;
              //   that.linkUrl = voiceUrl;
              // } else {
              //   voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
              //   that.linkUrl = voiceUrl;
              // }
              if (that.playType == 1) {
                voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
                that.linkUrl = voiceUrl;
              } else {
                voiceUrl = result.data.data;
                that.linkUrl = voiceUrl;
              }
              // #ifdef MP-WEIXIN
              innerAudioContext.obeyMuteSwitch = false;
              // #endif
              innerAudioContext.src = that.linkUrl;
              innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },
      playVoice() {
        let that = this;
        let count = that.configData.autoReviewNext == 1 ? that.configData.playbackCount : 0;
        let counter = 0;
        that.sayWord(that.displayData.word);
        innerAudioContext.onEnded(() => {
          counter++;
          console.log('播放完毕', counter);
          if (counter < count) {
            // setTimeout(() => {
            // }, 500);
            innerAudioContext.play();
          } else {
            innerAudioContext.offEnded();
            // that.disabled = false;

            // setTimeout(() => {
            //   that.disabled = false;
            // }, 500);
          }
        });
      },
      // 获取麦克风权限
      checkMicrophonePermission() {
        // #ifdef MP-WEIXIN
        // 微信小程序权限检查
        uni.getSetting({
          success: (res) => {
            console.log(res, 'checkMicrophonePermissioncheckMicrophonePermission');
            if (res.authSetting['scope.record'] === undefined) {
              // 首次申请权限
              uni.authorize({
                scope: 'scope.record',
                success: () => {
                  // 授权成功
                  this.voiceShow = false;
                },
                fail: () => {
                  // this.showAlert(); // 拒绝后提示
                  this.voiceShow = true;
                }
              });
            } else if (res.authSetting['scope.record'] === false) {
              // 已拒绝过，显示提示
              // this.showAlert();
              this.voiceShow = true;
            } else {
              this.voiceShow = false;
            }
            // 已授权则无需处理
          }
        });
        // #endif

        // #ifdef APP-PLUS
        // App端权限检查
        this.checkAppRecordPermission();
        // #endif

        // #ifdef H5
        // H5端不需要特殊权限检查
        this.voiceShow = false;
        // #endif
      },

      // App端录音权限检查
      // #ifdef APP-PLUS
      checkAppRecordPermission() {
        // 判断平台
        if (uni.getSystemInfoSync().platform === 'android') {
          // Android平台权限检查
          plus.android.requestPermissions(
            ['android.permission.RECORD_AUDIO'],
            (result) => {
              if (result.granted && result.granted.length > 0) {
                this.voiceShow = false;
              } else {
                this.voiceShow = true;
              }
            },
            (error) => {
              console.log('权限申请失败：', error);
              this.voiceShow = true;
            }
          );
        } else if (uni.getSystemInfoSync().platform === 'ios') {
          // iOS平台权限检查
          // iOS在录音时会自动弹出权限申请，这里直接设置为false
          this.voiceShow = false;
        } else {
          // 其他平台直接通过
          this.voiceShow = false;
        }
      },
      // #endif
      changeVoiceConfig(type) {
        if (type == 0) {
          this.voiceShow = false;
          setTimeout(() => {
            this.checkMicrophonePermission();
          }, 1000);
        } else {
          // #ifdef MP-WEIXIN
          // 微信小程序跳转至设置页
          uni.openSetting();
          // #endif

          // #ifdef APP-PLUS
          // App端重新申请权限
          this.checkAppRecordPermission();
          this.voiceShow = false;
          // #endif

          // #ifdef H5
          // H5端直接关闭弹窗
          this.voiceShow = false;
          // #endif
        }
      },
      // 说话时间短
      talkingShort() {
        console.log(213);
        innerAudioContext.stop();
        if (this.disabled) return;
        setTimeout(() => {
          uni.showToast({
            title: '说话时间太短啦',
            icon: 'none'
          });
        }, 500);
      },
      // 手指移开
      audioTouchmove(e) {
        let x = e.changedTouches[0].clientX;
        let y = e.changedTouches[0].clientY;
        this.audioXY = {
          x: x,
          y: y
        };
      },
      // 录音长按
      async startTalkLong() {
        var that = this;
        // if (that.isFirst) {
        //   this.showTopic1 = false;
        // }
        this.showTopic1 = false;
        if (that.disabled) return;
        innerAudioContext.stop();
        let arr = that.reviewList.filter((i) => i.word == that.displayData.word);
        if (arr && arr.length > 0) {
          that.reviewList = that.reviewList.filter((i) => i.word != that.displayData.word);
        }
        let arr1 = that.wordList.filter((o) => o.word == that.displayData.word);
        if (arr1 && arr1.length > 0) {
          that.wordList = that.wordList.filter((p) => p.word != that.displayData.word);
        }
        that.audioShow = true;
      },
      //授权录音
      accreditOption() {
        // #ifdef MP-WEIXIN
        // 微信小程序授权
        wx.authorize({
          scope: 'scope.record',
          success(res) {
            console.log('录音授权成功', res);
          },
          fail() {
            console.log('第一次录音授权失败');
            wx.showModal({
              title: '提示',
              content: '您未授权录音，功能将无法使用',
              showCancel: true,
              confirmText: '授权',
              confirmColor: '#AF1F25',
              success(res) {
                if (res.confirm) {
                  //确认则打开设置页面（自动切到设置页）
                  wx.openSetting({
                    success: (res) => {
                      console.log(res.authSetting);
                      if (!res.authSetting['scope.record']) {
                        console.log('未设置录音授权');
                        wx.showModal({
                          title: '提示',
                          content: '您未授权录音，功能将无法使用', // 可以自己编辑
                          showCancel: false,
                          success: function () {}
                        });
                      } else {
                        //第二次才成功授权
                        console.log('设置录音授权成功');
                      }
                    },
                    fail: function () {
                      console.log('授权设置录音失败');
                    }
                  });
                } else if (res.cancel) {
                  console.log('cancel');
                }
              },
              fail() {
                console.log('openfail');
              }
            });
          }
        });
        // #endif

        // #ifdef APP-PLUS
        // App端授权
        this.checkAppRecordPermission();
        // #endif

        // #ifdef H5
        // H5端不需要特殊授权
        console.log('H5端录音授权');
        // #endif
      },
      // 取消发送语音
      async closeAudio(e) {
        console.log(e, 'eeeeeeeeeeeeeeeeeeeeeeee');
        let that = this;
        // if (e == 1) {
        //   that.disabled = true;
        // } else {
        //   that.disabled = false;
        // }
        that.audioTouchendShow = false;
        that.audioShow = false;
      },

      // 发送语音
      async submitAudio(url, savedFilePath) {
        console.log(url, savedFilePath);
        let that = this;
        that.disabled = true;
        if (url) {
          uni.showLoading({
            title: '发音中',
            duration: 1500
          });
          that.wordList.push({
            audioUrl: url,
            word: that.displayData.word,
            chinese: that.displayData.translation,
            scheduleCode: that.displayData.scheduleCode,
            nowRound: that.displayData.lastReviewCycle,
            type: 1
          });
          that.originList.forEach((i) => {
            if (i.word == that.displayData.word) {
              that.$set(i, 'hasResult', true);
            }
          });
          console.log(that.originList, ' that.originList that.originList that.originList');
          var model = {
            word: that.displayData.word,
            scheduleCode: that.displayData.scheduleCode,
            correct: 0,
            translation: that.displayData.translation,
            lastReviewCycle: that.displayData.lastReviewCycle,
            lastReviewDate: that.displayData.lastReviewDate,
            lastStudyTime: that.displayData.lastStudyTime,
            nextReviewDate: that.displayData.nextReviewDate,
            nowRound: that.displayData.lastReviewCycle
          };
          that.reviewList.push(model);
          setTimeout(() => {
            that.disabled = false;
            if (that.configData.autoReviewNext == 1 && that.originIndex + 1 < this.originList.length) {
              that.originIndex++;
            } else if (that.configData.autoReviewNext != 1) {
              that.fingleShow = true;
              that.showTopic = false;
            }
          }, 1500);
        } else {
          that.disabled = false;
          uni.showToast({
            title: '发音失败',
            icon: 'none'
          });
        }
      },

      changeChinese() {
        if (this.showChinese) {
          this.showChineseClass = !this.showChineseClass;
        } else {
          return false;
        }
      },
      countDomnFn() {
        let that = this;
        that.overlayShow = true;
        that.countDown = setInterval(() => {
          if (that.times == 0) {
            that.overlayShow = false;
            clearInterval(that.countDown);
            that.countDown = null;
            uni.setStorageSync('aiReviewStartTime', new Date(Date.now()).getTime());
            setTimeout(() => {
              that.playVoice();
            }, 200);
            return;
          }
          that.times--;
        }, 1000);
      },

      clearStatus() {
        this.isFalse = false;
        this.isRight = false;
        // this.disabled = true;
      },
      goSetting() {
        this.showTopic = false;
        innerAudioContext.stop();
        uni.navigateTo({
          url: `/antiAmnesia/review/aiReviewSetting?studentCode=${this.studentCode}`
        });
      },
      stopTouch(e) {
        console.log(e);
        if (this.originIndex + 1 == this.originList.length) {
          // that.isDisabled = true;
          uni.showToast({
            title: '已经是最后一个啦',
            icon: 'none'
          });
        }
      },
      /**
       * swiper滑动时候
       */
      swiperChange(event) {
        let that = this;
        innerAudioContext.stop();
        let { current } = event.detail;
        console.log(current, that.originIndex);
        that.isDisabled = false;
        that.fingleShow = false;
        that.showTopic1 = false;
        that.showTopic = false;
        that.originIndex = current;
        that.displayData = that.originList[current];
        that.clearStatus();
        that.playVoice();
      },
      changeFinishConfig(type) {
        // #ifdef MP-WEIXIN
        // 微信小程序埋点
        if (getApp().sensors) {
          getApp().sensors.track('antiAmnesiaClick', {
            name: type == 1 ? '继续复习' : '结束复习'
          });
        }
        // #endif

        // #ifdef APP-PLUS
        // App端埋点（如果有其他埋点SDK）
        console.log('App端埋点：antiAmnesiaClick', {
          name: type == 1 ? '继续复习' : '结束复习'
        });
        // #endif

        if (type == 1) {
          this.finishToast = false;
          return;
        } else {
          this.finishToast = false;
          innerAudioContext.stop();
          this.finishReview();
        }
      },
      async changeForgetConfig(type) {
        let that = this;
        if (type == 1) {
          this.forgetToast = false;
          // this.onlyCreateReport();
          let str = await that.validateFn();
          // let arr = str ? str.split('-') : [];
          if (str) {
            let reportId = str;
            that.onlyCreateReport(reportId);
          } else {
            return false;
          }
        } else {
          this.forgetToast = false;
          // this.createReport();
          let str = await that.validateFn();
          // let arr = str ? str.split('-') : [];
          if (str) {
            // let reportId = arr[1];
            let reportId = str;
            that.createReport(reportId);
          } else {
            return false;
          }
        }
      },
      changeBackConfig(type) {
        this.isShow = true;
        if (type == 1) {
          this.backToast = false;
          return;
        } else {
          innerAudioContext.stop();
          this.finishReview();
        }
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #f5f8fa;
  }

  /* App端专用样式 */
  /* #ifdef APP-PLUS */
  .main-content {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .app-swiper {
    /* App端swiper优化 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
    overflow: hidden !important;
  }
  /* #endif */

  /* 导航栏样式优化 */
  .nav-setting-text {
    font-size: 24rpx;
    color: #000;
    /* #ifdef APP-PLUS */
    font-size: 28rpx;
    /* #endif */
  }

  .page-content {
    border-radius: 14rpx;
    // width: calc(100% - 60rpx);
    background-color: #f5f8fa;
    padding: 30rpx 0 30rpx 0;
  }

  .swiper-content {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    /* #ifdef APP-PLUS */
    justify-content: space-between;
    padding: 40rpx 0;
    /* #endif */
  }
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
    z-index: 999;
  }
  .word {
    // margin-top: 100rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding-bottom: 200rpx;
    /* #ifdef APP-PLUS */
    padding-bottom: 80rpx;
    justify-content: center;
    /* #endif */
    .toast {
      font-size: 24rpx;
      color: #b0b0b0;
    }
    .english {
      font-size: 56rpx;
      color: #333333;
      font-weight: 600;
      /* #ifdef APP-PLUS */
      font-size: 60rpx;
      letter-spacing: 2rpx;
      margin: 40rpx 0;
      /* #endif */
    }
    .chinese {
      width: 100%;
      font-size: 40rpx;
      color: #333333;
      font-weight: 600;
    }
  }
  .right-false {
    width: 100%;
    margin-top: 200rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    image {
      width: 266rpx;
      height: 80rpx;
    }
  }
  .start-talking {
    width: 150rpx;
    height: 50rpx;
    background: #f1fffa;
    border-radius: 50rpx;
    border: 2rpx solid #4fb693;
    transition: all 0.2s ease;
    /* #ifdef APP-PLUS */
    margin-top: 20rpx;
    /* #endif */
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #4fb693;
      font-size: 28rpx;
    }
  }

  /* 按钮hover效果 */
  .button-hover {
    background: #e8f8f2 !important;
    transform: scale(0.95);
  }
  .footer_show {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    margin-top: 100rpx;
    .start-talking {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .start-talk {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
    .showChinese {
      width: 300rpx;
      height: 100rpx;
      background: #f1fffa;
      border-radius: 50rpx;
      border: 2rpx solid #4fb693;
      .showChinese_btn {
        width: 100%;
        height: 100%;
        justify-content: center;
        color: #4fb693;
        font-size: 28rpx;
      }
    }
  }
  .toast {
    font-size: 24rpx;
    color: #b0b0b0;
  }
  .toastgreen {
    font-size: 24rpx;
    color: #4fb693;
  }
  .disabled {
    background: #f8f8f8 !important;
    border: 2rpx solid #acacac !important;
  }
  .disabled-btn {
    color: #a6a6a6 !important;
  }
  .footer {
    width: 100%;
    position: absolute;
    bottom: 100rpx;
    display: flex;
    justify-content: center;
    align-content: center;
    margin-top: 60rpx;
    /* #ifdef APP-PLUS */
    bottom: calc(60rpx + env(safe-area-inset-bottom));
    position: relative;
    margin-top: 40rpx;
    /* #endif */
    .footer_button {
      width: 600rpx;
      height: 92rpx;
      line-height: 92rpx;
      text-align: center;
      color: #fff;
      background: #428a6f;
      border-radius: 46rpx;
      border: 1px solid #428a6f;
      font-size: 32rpx;
      transition: all 0.2s ease;
      /* #ifdef APP-PLUS */
      box-shadow: 0 4rpx 12rpx rgba(66, 138, 111, 0.3);
      /* #endif */
    }
    .start-talk {
      width: 100%;
      height: 100%;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }

  /* 录音按钮hover效果 */
  .record-button-hover {
    background: #3a7a5f !important;
    transform: scale(0.98);
    /* #ifdef APP-PLUS */
    box-shadow: 0 2rpx 8rpx rgba(66, 138, 111, 0.5) !important;
    /* #endif */
  }
  .title-bg {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 90rpx;
    background: #f6f9fc;
    border-radius: 8rpx;
  }

  // .start-talk {
  //   width: 240rpx;
  //   height: 60rpx;
  //   display: flex;
  //   justify-content: center;
  //   align-items: center;
  //   background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  //   border-radius: 14rpx;
  // }

  .word-play {
    padding: 10rpx 24rpx;
    height: 40rpx;
    background: #f3f3f3;
    border-radius: 18rpx;
  }
  .warp {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .warp1 {
    height: 100%;
    .title {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
    .title-center {
      font-size: 28rpx;
      color: #17ca8a;
      font-weight: 600;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .finger {
      position: absolute;
      right: 0;
      bottom: 300rpx;
      .title {
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 10rpx;
      }
    }
  }
  .rect {
    color: #fff;
    font-weight: bold;
    .number {
      font-size: 300rpx;
      margin-top: 50rpx;
    }
    .text {
      font-size: 40rpx;
      margin-top: 10rpx;
    }
  }
  .blur {
    // filter: blur(16rpx);
    // pointer-events: none;
    font-size: 40rpx;
    color: #333333;
    filter: blur(12rpx); /* 添加模糊效果 */
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .cancel {
      background: #f1f1f1;
      color: #555;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .topicToast-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
  }
  .topicToast {
    background-color: #626262;
    color: #fff;
    font-weight: 600;
    font-size: 28rpx;
    width: 600rpx;
    height: 50rpx;
    line-height: 50rpx;
    border-radius: 20rpx;
    // position: absolute;
    // top: 120rpx;
    // left: 50%;
    // z-index: 99;
    // transform: translateX(-50%);
  }
  .overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #808080;
    /* #ifdef APP-PLUS */
    padding-bottom: env(safe-area-inset-bottom);
    /* #endif */
  }

  .overlay-image {
    height: 100%;
    /* #ifdef APP-PLUS */
    max-height: calc(100% - env(safe-area-inset-bottom));
    /* #endif */
  }
  .finger {
    position: absolute;
    right: 0;
    bottom: 300rpx;
    .title {
      font-size: 28rpx;
      color: #000;
      font-weight: 600;
      margin-bottom: 10rpx;
    }
  }

  .topicToastBottom-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: absolute;
    right: -9%;
    // top: 150rpx;
    top: 10%;
    // transform: translateX(-50%);
    width: 620rpx;
    z-index: 99;
    // background-color: #f0f0f0;
    // padding: 20rpx 28rpx;
    // border-radius: 24rpx;
    // font-size: 24rpx;
    // font-weight: 600;
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.6);
  }
  .chat-bubble {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 70%;
    margin: 20rpx auto;
    position: relative;
  }
  .bubble-content {
    background-color: #f0f0f0;
    padding: 20rpx 28rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    font-weight: 600;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.6);
  }

  .arrow-top {
    position: relative;
    width: 4rpx;
    height: 100rpx;
    margin: 0 auto;
    background: repeating-linear-gradient(to top, transparent, transparent 10rpx, #999 10rpx, #999 20rpx);
  }
  .arrow-top::after {
    content: '';
    position: absolute;
    top: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-bottom: 20rpx solid #999;
  }
  .arrow-down {
    position: relative;
    width: 4rpx;
    height: 100rpx;
    margin: 0 auto;
    background: repeating-linear-gradient(to bottom, transparent, transparent 10rpx, #999 10rpx, #999 20rpx);
  }

  .arrow-down::after {
    content: '';
    position: absolute;
    bottom: -10rpx; /* ⬅️ 让箭头出现在底部 */
    left: 50%;
    transform: translateX(-50%);
    border-left: 12rpx solid transparent;
    border-right: 12rpx solid transparent;
    border-top: 20rpx solid #999; /* ⬅️ 朝下箭头 */
  }
</style>
<style>
  .u-navbar__content__right {
    right: 200rpx !important;
  }
  .u-nav-slot {
    width: 100%;
  }
  .u-nav-slot-center {
    width: 100%;
    text-align: left;
    padding-left: 100rpx;
  }
  .locked-swiper {
    touch-action: none !important; /* 禁止所有手势 */
    pointer-events: none !important; /* 禁止点击事件（可选） */
  }

  /* App端强制swiper高度控制 */
  /* #ifdef APP-PLUS */
  swiper {
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
  }

  .app-swiper swiper-item {
    height: 100% !important;
    max-height: 1400rpx !important;
    min-height: 1000rpx !important;
    overflow: hidden !important;
  }
  /* #endif */
</style>