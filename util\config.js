// let DXHost = 'http://*************:8081/';
let DXHost = 'https://test179.ngrok.dxznjy.com/';
// let DXHost = 'https://uat-gateway.dxznjy.com/';
// let DXHost = 'https://189test.ngrok.dxznjy.com/';
const match = /:\/\/([^\/?#]+)/i.exec(DXHost);
let host = match ? match[1] : '';
const isTest179 = host.startsWith('test179');
const isTest189 = host.startsWith('189test');
console.log('179', isTest179);
console.log('189', isTest189);
// let DXHost = 'https://gateway.dxznjy.com/'; //上线
// let DXHost = 'https://linetest.dxznjy.com/'; //线上测试

// let DXHost = 'https://gateway.dxznjy.com/'; //上线
// let DXHost = 'https://uat-gateway.dxznjy.com/'; //线上测试
// scrm 地址
// let DXSCRMHost = "https://applet.dxznjy.com/";
let DXSCRMHost = 'https://scrm.dxznjy.com/';

// let curUseApp = 'zx'; ///!!! 当前使用的app  B端 or 甄选
let curUseApp = 'b'; ///!!! 当前使用的app  B端 or 甄选

// 甄选B端/C端APP下载h5地址
// let zxDownH5Url = curUseApp=="b"?'https://dxx-share.dxznjy.com':'https://zx-share.dxznjy.com'; //线上
let zxDownH5Url = curUseApp == 'b' ? ' https://bdownloadh5.ngrok.dxznjy.com' : 'http://zxdownh5.ngrok.dxznjy.com'; //测试

let ImgsomeHost = 'https://document.dxznjy.com/';
let ImguseHost = 'https://document.dxznjy.com/applet/';
let contactId = 'wwcc79054d80e112df'; //微信客服id
let contactUrl = 'https://work.weixin.qq.com/kfid/kfc67db783b65cf6f20';

let miniOriginalId = 'gh_d43ada85807b';
let webUrl = 'http://www.dxznjy.com/';
let webNewUrl = 'https://document.dxznjy.com/dxSelectH5/';
// let webNewUrl = "https://document.dxznjy.com/dxSelectH5-Second/"
let AppwebNewUrl = 'https://document.dxznjy.com/appDXSelectH5/';

let supermanShareImage = 'https://document.dxznjy.com/dxSelect/fourthEdition/superman_share.png';
let supermanClubShareImage = 'https://document.dxznjy.com/dxSelect/fourthEdition/supermanClub_share.png';

let ClassStyle = [{
    key: '1',
    courseName: '英语',
    color: '#94D6F3'
  },
  {
    key: '2',
    courseName: '概率论',
    color: '#CCB2FF'
  },
  {
    key: '3',
    courseName: '选修课',
    color: '#F0C762'
  },
  {
    key: '4',
    courseName: '创业基础',
    color: '#FDCDC5'
  },
  {
    key: '5',
    courseName: '生物化学',
    color: '#FFA7C5'
  },
  {
    key: '6',
    courseName: '马克思主义',
    color: '#CF93D8'
  },
  {
    key: '7',
    courseName: '形式与策略',
    color: '#92B5E6'
  }
];
let XKTCurriculumCodeArr = ['XKT', 'XKT_CZHX1', 'XKT_CZSX1', 'XKT_CZWL1', 'XKT_CZYW1', 'XKT_CZYY1']; //学考通相关课程大类
let XSMCurriculumCodeArr = ['XSM_CZDL1', 'XSM_CZLS1', 'XSM_CZSW1', 'XSM_CZZZ1', 'XSM_SDHK', 'XSM_ZSZK']; //小四门相关课程大类
let MATHCurriculumCodeArr = ['MATH']; //数学相关课程大类
let CurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr, ...MATHCurriculumCodeArr]; //学考通、小四门所有课程大类
let AmapKey = 'b5c3eae1d3a4990c61c73da366593720';

module.exports = {
  // ApiHost: ApiHost,
  // ImgHost: ImgHost,
  AmapKey: AmapKey,
  DXHost: DXHost,
  DXSCRMHost: DXSCRMHost,
  ImgsomeHost: ImgsomeHost,
  ImguseHost: ImguseHost,
  ClassStyle: ClassStyle,
  curUseApp: curUseApp,

  contactId: contactId,
  contactUrl: contactUrl,
  miniOriginalId: miniOriginalId,
  webUrl: webUrl,
  webNewUrl: webNewUrl,
  AppwebNewUrl: AppwebNewUrl,
  supermanShareImage: supermanShareImage,
  supermanClubShareImage: supermanClubShareImage,
  XKTCurriculumCodeArr,
  XSMCurriculumCodeArr,
  CurriculumCodeArr,
  MATHCurriculumCodeArr,
  zxDownH5Url: zxDownH5Url,
  isTest179: isTest179,
  isTest189: isTest189
};