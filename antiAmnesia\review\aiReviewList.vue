<template>
  <view>
    <u-navbar title=" " :safeAreaInsetTop="true" placeholder>
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="14"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center">复习单词</view>
      </view>
      <view class="u-nav-slot1" slot="right" @click="goSetting">
        <u-icon name="setting" color="#000" bold size="15"></u-icon>
        <view style="font-size: 16rpx; color: #000">设置</view>
      </view>
    </u-navbar>
    <view class="plr-30 t-c" :style="{ height: useHeight - 100 + 'rpx' }" v-show="!toastShow">
      <view class="page-content" :style="{ height: useHeight - 280 + 'rpx' }">
        <view class="" style="height: 100%">
          <u-list @scrolltolower="scrolltolower" pagingEnabled height="95%">
            <u-list-item v-for="(item, index) in originList" :key="index">
              <u-cell title=" ">
                <text slot="label" class="u-slot-value">{{ item.word }}</text>
              </u-cell>
            </u-list-item>
          </u-list>
        </view>
      </view>
      <view class="footer">
        <view class="" @click="goDetail(1)">
          <image src="https://document.dxznjy.com/course/b4cf39519a714238a624d7e92105ff55.png" mode=""></image>
        </view>
        <view class="" @click="goDetail(2)">
          <image src="https://document.dxznjy.com/course/4f8e32e5c26b47598424ada54b2c3901.png" mode=""></image>
        </view>
      </view>
    </view>
    <view class="overlay" :style="{ height: useHeight - 230 + 'rpx' }" v-show="toastShow" @click="startFn">
      <image src="https://document.dxznjy.com/course/f90a6d006eea44a59dfd4b4bf5c2bc95.png" style="height: 100%" mode="aspectFill"></image>
    </view>
    <u-modal :show="voiceShow" title=" " showCancelButton>
      <view class="slot-content">
        <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">“单词抗遗忘“</view>
        <view style="color: #555; font-weight: 600; font-size: 32rpx; margin-bottom: 15rpx">想访问您的麦克风</view>
        <view class="modal-title">使用麦克风发音复习和指令控制</view>
      </view>
      <view class="confirmButton" slot="confirmButton">
        <view class="btn cancel" @click="changeVoiceConfig(0)">不允许</view>
        <view class="btn confirm" @click="changeVoiceConfig(1)">允许</view>
      </view>
    </u-modal>
    <!--    <u-overlay :show="toastShow" :opacity="0.5" @click="startFn">
      <view class="warp1">
        <view class="finger">
          <view class="flex-a-c" style="justify-content: center">
            <view class="title">点击乱序或正序，开启你的复习之旅！</view>
          </view>

          <image src="https://document.dxznjy.com/course/10516a3dffd444259ac485caa80bff6a.png" style="width: 60rpx; height: 80rpx" mode=""></image>
        </view>
      </view>
    </u-overlay> -->
    <!--    <u-overlay :show="toastShow" :opacity="1" @click="startFn">
      <image src="https://document.dxznjy.com/course/f90a6d006eea44a59dfd4b4bf5c2bc95.png" style="width: 100%; height: 100%" mode="scaleToFill"></image>
    </u-overlay> -->
  </view>
</template>

<script>
  // const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        useHeight: 0,
        studentCode: '',
        status: 'loadmore',
        voiceShow: false,
        // 源数据
        originList: [],
        pageindex: 1, //当前页
        pageSize: 20, //页数
        autoplay: false,
        interval: 10000,
        batchId: '',
        wordCount: 0,
        isLoading: false, // 加载状态
        toastShow: true,
        configData: {}
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },
    onLoad(e) {
      this.studentCode = 'G00915';
      if (e) {
        this.studentCode = e.studentCode;
        this.batchId = e.batchId;
        // this.loadWordList();
        this.loadAllData();
        this.getConfigDetail();
      }
      this.toastShow = true;
    },
    onShow() {
      this.checkMicrophonePermission();
    },
    methods: {
      async getConfigDetail() {
        let res = await this.$httpUser.get('znyy/word/review/play-config', {
          // todo 学员 code 固定值 用于测试
          studentCode: this.studentCode
          // studentCode: '6231217888',
        });
        if (res && res.data.success) {
          console.log(res.data.data);
          let isFirstAi = res.data.data.isFirst;
          uni.setStorageSync('isFirstAi', isFirstAi);
          this.toastShow = isFirstAi;
          const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
          this.configData = converted;
          uni.setStorageSync('aiConfigData', converted);
        }
      },
      startFn() {
        this.toastShow = false;
      },
      // 获取麦克风权限
      checkMicrophonePermission() {
        uni.getSetting({
          success: (res) => {
            console.log(res, 'checkMicrophonePermissioncheckMicrophonePermission');
            if (res.authSetting['scope.record'] === undefined) {
              // 首次申请权限
              uni.authorize({
                scope: 'scope.record',
                success: () => {
                  // 授权成功
                  this.voiceShow = false;
                },
                fail: () => {
                  // this.showAlert(); // 拒绝后提示
                  this.voiceShow = true;
                }
              });
            } else if (res.authSetting['scope.record'] === false) {
              // 已拒绝过，显示提示
              // this.showAlert();
              this.voiceShow = true;
            } else {
              this.voiceShow = false;
            }
            // 已授权则无需处理
          }
        });
      },
      changeVoiceConfig(type) {
        if (type == 0) {
          this.voiceShow = false;
          setTimeout(() => {
            this.checkMicrophonePermission();
          }, 1000);
        } else {
          // 跳转至设置页
          uni.openSetting();
          // this.accreditOption();
        }
      },
      async loadAllData() {
        let that = this;
        if (that.isLoading) return;
        that.isLoading = true;
        uni.showLoading({
          title: '加载中'
        });
        try {
          await that.loadWordList();
          if (that.originList.length == that.wordCount.totalWordCount) {
            return (that.status = 'nomore');
          } else {
            that.pageindex++;
            that.isLoading = false;
            await that.loadAllData();
          }
        } catch (error) {
          uni.showToast({ title: '加载失败', icon: 'none' });
        } finally {
          uni.hideLoading();
          that.isLoading = false;
        }
      },
      goDetail(type) {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('antiAmnesiaClick', {
          name: type == 1 ? '乱序复习' : '正序复习'
        });
        // #endif
        let that = this;
        let aiWordData = [];
        if (type == 1) {
          aiWordData = that.shuffle(that.originList);
        } else {
          aiWordData = that.originList;
        }
        aiWordData.forEach((item) => {
          item.hasResult = false;
        });
        if (aiWordData.length < that.wordCount.totalWordCount || aiWordData.length == 0) return;
        this.handleSave();
        // 存储数据
        uni.setStorage({
          key: 'aiWordData',
          data: aiWordData,
          success() {
            uni.navigateTo({
              url: `/antiAmnesia/review/aiReviewDetail?studentCode=${that.studentCode}&batchId=${that.batchId}&wordCount=${that.wordCount.totalWordCount}`
            });
          }
        });
      },
      // 保存配置
      async handleSave() {
        this.configData.isFirst = false;
        await this.$httpUser.post('znyy/word/review/play-config', this.configData);
        // 这里可添加提交到服务器的逻辑
      },
      shuffle(array) {
        const copy = [...array]; // 创建副本
        for (let i = copy.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [copy[i], copy[j]] = [copy[j], copy[i]];
        }
        return copy;
      },
      async loadWordList() {
        var that = this;
        var mindex = this.pageindex;
        let wordCountResult = await that.$httpUser.get('znyy/review/query/fun/word/count', {
          studentCode: that.studentCode
        });
        if (wordCountResult && wordCountResult.data.success) {
          that.wordCount = wordCountResult.data.data;
        }
        let result = await that.$httpUser.get('znyy/review/query/fun/word/' + mindex + '/' + this.pageSize + '/' + this.studentCode);
        if (result) {
          if (result.data.data.data.length == 0) {
            that.status = 'nomore';
            // that.footerShow = 1;
          } else {
            if (result.data.data.data.length > 0) {
              that.originList = that.originList.concat(result.data.data.data);
            }
            that.status = that.pageindex >= Number(result.data.data.totalPage) ? 'nomore' : 'loadmore';
          }
        }
      },
      scrolltolower() {
        console.log('触底了', '1');
        let that = this;
        if (that.originList.length == that.wordCount.totalWordCount) {
          return (that.status = 'nomore');
        } else {
          that.pageindex++;
          that.loadWordList();
        }
      },
      goSetting() {
        // #ifdef MP-WEIXIN
        getApp().sensors.track('antiAmnesiaClick', {
          name: '设置'
        });
        // #endif
        console.log('设置设置');
        uni.navigateTo({
          url: `/antiAmnesia/review/aiReviewSetting?studentCode=${this.studentCode}`
        });
      },
      goBack() {
        uni.navigateBack();
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #fff;
  }
  .page-content {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .title-bg {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 90rpx;
    background: #f6f9fc;
    border-radius: 8rpx;
  }

  .word-play {
    padding: 10rpx 24rpx;
    height: 40rpx;
    background: #f3f3f3;
    border-radius: 18rpx;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: space-around;
    // margin-top: 10rpx;
    image {
      width: 346rpx;
      height: 80rpx;
    }
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .cancel {
      background: #f1f1f1;
      color: #555;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .warp1 {
    height: 100%;
    .title {
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 600;
    }
    .title-center {
      font-size: 28rpx;
      color: #17ca8a;
      font-weight: 600;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .finger {
      position: absolute;
      right: 40rpx;
      bottom: 300rpx;

      .title {
        font-size: 34rpx;
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 20rpx;
      }
    }
  }
  .overlay {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #808080;
  }
</style>
<style>
  .u-navbar__content__right {
    right: 180rpx !important;
  }
  .u-nav-slot {
    width: 100%;
  }
  .u-nav-slot-center {
    width: 100%;
    text-align: left;
    padding-left: 100rpx;
  }
</style>
